module.exports = {
  "plugins": [
    "vue",
  ],
  "extends": [
    "eslint:recommended",
    "plugin:vue/vue3-recommended",
  ],
  "env": {
    "browser": true,
    "node": true,
    "es6": true,
  },
  "parser": "vue-eslint-parser",
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module",
    "ecmaFeatures": {
      "jsx": true,
    },
    "allowImportExportEverywhere": false,
  },
  "rules": {
    "semi": 2,
    "indent": [2, 2],
    "eqeqeq": 2,
    "comma-dangle": [2, "always-multiline"],
    "quotes": 2,
    "no-undef": 0,
    "vue/multi-word-component-names": 0,
    "vue/max-attributes-per-line": 0,
  },
};