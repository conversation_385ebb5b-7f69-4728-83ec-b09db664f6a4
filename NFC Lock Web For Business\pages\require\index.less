.page {
  width: 100%;
  height: 100%;

  :deep(.page-content) {
    padding: 0rpx;
  }

  .empty-box {
    height: 100%;
  }

  .apply-list {
    height: 100%;

    .list-item {
      background-color: #ffffff;
      color: #333;
      padding: 28rpx;
      border-radius: 16rpx;
      box-shadow: 0rpx 0rpx 40rpx #3080F722;
      margin: 0rpx 32rpx;
      margin-top: 20rpx;

      .wrap {
        font-weight: bold;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &.main {
          margin-bottom: 10rpx;
        }

        &.sub {
          font-size: 85%;
          font-weight: normal;
        }

        .status {
          color: #3080F7;

          &[status="success"] {
            color: #87d068;
          }

          &[status="error"] {
            color: #ff4d4f;
          }

          &[status="normal"] {
            color: #3080F7;
          }
        }

        .title {
          font-weight: bold;
          width: 130rpx;
          text-align-last: justify;
        }

        .text {
          flex: 1;
          margin-left: 20rpx;
        }
      }
    }
  }

  .btn-group {
    padding: 32rpx;
    padding-top: 0rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .btn {
      width: 100%;
      height: 80rpx;
      margin-top: 20rpx;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
  
      &.success {
        background-color: #87d068;
        color: #fff;
      }
  
      &.error {
        background-color: #ff4d4f;
        color: #fff;
      }
  
      &.normal {
        border: 1px solid #888888;
        color: #888888;
      }
    }
  }
}

