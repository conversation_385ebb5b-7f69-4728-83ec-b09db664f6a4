.page {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #F6F6F6;
  
  .background {
    position: absolute;
    width: 100%;
    height: 560rpx;
    background: linear-gradient(to bottom, #3080F7, #b5b3fc);
    background-size: contain;
    background-position: top center;
    background-repeat: no-repeat;
  }
  
  .app-info {
    width: 100%;
    height: 400rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
    
    .version {
      color: white;
    }

    image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 20rpx;
    }
  }
  
  .buttons {
    background-color: white;
    border-radius: 16rpx;
    box-shadow: 0rpx -4rpx 20rpx 0rpx rgba(0,0,0,0.03);
    
    >:nth-child(n) {
      position: relative;
      padding: 32rpx;
      font-size: 28rpx;
      border-radius: 6rpx;
      border-bottom: 4rpx solid #fafafa;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    >:last-child {
      border: none;
    }
    
    .hint-text {
      color: gray;
      font-size: 90%;
    }
    
    .mail {
      color: #BBBBBB;
    }
  }
}