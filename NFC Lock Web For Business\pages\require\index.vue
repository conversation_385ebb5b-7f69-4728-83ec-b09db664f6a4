<template>
  <view class="page">
    <base-layout
      page-title="申请"
      :selected-index="3"
      @user-info="(info) => userInfo = info"
    >
      <template #content>
        <scroll-view
          v-if="applyList.length"
          class="apply-list"
          scroll-y
          @scrolltolower="onApplyListToLower"
        >
          <view
            v-for="(item, index) in applyList"
            :key="index"
            class="list-item"
            @click="onApplyItemClick(item)"
          >
            <view class="wrap main">
              <text class="datetime">
                {{ item.created_at }}
              </text>
              <text class="status" :status="applyStatus[item.is_pass].status">
                {{ applyStatus[item.is_pass].text }}
              </text>
            </view>
            <view class="wrap sub">
              <text class="title">
                设备名称
              </text>
              <text class="text">
                {{ item.device_name }}
              </text>
            </view>
            <view class="wrap sub">
              <text class="title">
                设备ID
              </text>
              <text class="text">
                {{ item.entity_id }}
              </text>
            </view>
            <view class="wrap sub">
              <text class="title">
                位置
              </text>
              <text class="text">
                {{ item.addr }}
              </text>
            </view>
            <view class="wrap sub">
              <text class="title">
                申请人
              </text>
              <text class="text">
                {{ item.username }}
              </text>
            </view>
            <view class="wrap sub">
              <text class="title">
                申请时限
              </text>
              <text class="text">
                {{ expireTimes[item.expires_type] }}
              </text>
            </view>
            <view class="wrap sub">
              <text class="title">
                授权人
              </text>
              <text class="text">
                {{ item.passer_username }}
              </text>
            </view>
            <!-- <view class="wrap sub">
              <text class="title">
                作业类型
              </text>
              <text class="text">
                抢修
              </text>
            </view> -->
            <view class="wrap sub">
              <text class="title">
                备注
              </text>
              <text class="text">
                {{ item.reason }}
              </text>
            </view>
          </view>
          <br>
        </scroll-view>
        <uv-empty v-else class="empty-box" name="list" icon-size="110" text-size="15" text="暂无数据" />

        <uv-modal ref="applyModal" title="提示" content="是否通过该设备的权限申请？">
          <template #confirmButton>
            <div class="btn-group">
              <div class="btn success" @click="onApplyPassOrRefuse(true)">
                通过
              </div>
              <div class="btn error" @click="onApplyPassOrRefuse(false)">
                拒绝
              </div>
              <div class="btn normal" @click="onApplyCancel">
                取消
              </div>
            </div>
          </template>
        </uv-modal>
      </template>
    </base-layout>
  </view>
</template>

<script setup>
import { ref, watch } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { DeviceApplyListApi, DeviceApplyUpdateApi } from "@/utils/api.js";
import { uniModal } from "@/utils/util.js";
import { applyStatus, expireTimes } from "./data.js";
import { getVarientConfig } from "@/varient.js";

import BaseLayout from "@/components/base-layout/index.vue";

const userInfo = ref({});
const applyModal = ref(null);
const applyList = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const selectedItem = ref(null);

const initDatas = async () => {
  const api = new DeviceApplyListApi().token(userInfo.value.token).data({
    page: currentPage.value,
    page_size: pageSize.value,
  });
  await api.send();
  const result = await api.getData();
  applyList.value = [...applyList.value, ...result.data.list];
  total.value = result.data.total;
};

const onApplyListToLower = () => {
  if (applyList.value.length < total.value) {
    currentPage.value += 1;
    initDatas();
  }
};

const onApplyItemClick = (item) => {
  if (Number(item.is_pass) === 1 && userInfo.value.role_id === 1) {
    selectedItem.value = item;
    applyModal.value.open();
  }
};

const onApplyPassOrRefuse = async (isPass) => {
  const api = new DeviceApplyUpdateApi().token(userInfo.value.token).data({
    id: selectedItem.value.id,
    is_pass: isPass ? 2 : 3,
  });
  await api.send();
  const result = await api.getData();
  if (result.code === 200) {
    applyModal.value.close();
    const modifiedItem = applyList.value.find(item => item.id === selectedItem.value.id);
    modifiedItem.is_pass = isPass ? 2 : 3;
    uniModal("操作成功");
  } else {
    uniModal("操作失败");
  }
};

const onApplyCancel = () => {
  applyModal.value.close();
};

watch(() => userInfo.value, () => {
  initDatas();
});

onLoad(() => {
  const varientConfig = getVarientConfig();
  if (varientConfig.customRequirePageZGHN) {
    uni.redirectTo({
      url: "/pages/require/custom-zghn/index",
    });
  } else {
    showPage.value = true;
  }
});

onShow(() => {

});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>