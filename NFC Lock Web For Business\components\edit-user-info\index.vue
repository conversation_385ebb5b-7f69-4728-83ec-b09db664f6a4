<template>
  <my-drawer
    :title="pageTitle"
    :height="900"
    :open="show"
    @close="onBack"
  >
    <template #content>
      <view class="form">
        <view class="form-box">
          <template v-if="type === 'username'">
            <view class="form-item">
              <uv-input
                v-model="formModel.username"
                :placeholder="i18n('placeholderUsername')"
                border="none"
                maxlength="10"
                :custom-style="{ backgroundColor: '#f6f6f6', padding: '24rpx' }"
              />
            </view>
          </template>

          <template v-if="type === 'password'">
            <view class="form-item">
              <uv-input
                v-model="formModel.password"
                type="password"
                :placeholder="i18n('placeholderPassword')"
                border="none"
                maxlength="16"
                :custom-style="{ backgroundColor: '#f6f6f6', padding: '24rpx' }"
              />
            </view>
            <view class="form-item">
              <uv-input
                v-model="formModel.rePassword"
                type="password"
                :placeholder="i18n('placeholderPasswordAgain')"
                border="none"
                maxlength="16"
                :custom-style="{ backgroundColor: '#f6f6f6', padding: '24rpx' }"
              />
            </view>
          </template>
          
          <template v-if="type === 'deviceName'">
            <view class="form-item">
              <uv-input
                v-model="formModel.deviceName"
                :placeholder="i18n('placeholderDeviceName')"
                border="none"
                maxlength="10"
                :custom-style="{ backgroundColor: '#f6f6f6', padding: '24rpx' }"
              />
            </view>
          </template>
        </view>
        <view class="btns">
          <view @click="onSubmit">
            {{ i18n("btnConfirm") }}
          </view>
        </view>
      </view>
    </template>
  </my-drawer>
</template>

<script setup>
import { computed, ref, watch } from "vue";
import { useI18n } from "vue-i18n";

import MyDrawer from "../drawer/index.vue";

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "",
  },
  form: {
    type: Object,
    default: () => ({}),
  },
});

const emits = defineEmits(["submit", "update:show", "update:form"]);

const { tm: i18n } = useI18n();
const formModel = ref({});

const onSubmit = () => {
  emits("update:form", formModel.value);
  emits("submit");
};

const onBack = () => {
  emits("update:show", false);
};

const pageTitle = computed(() => {
  if (props.type === "username") {
    return i18n("componentNameEditUsername");
  } else if (props.type === "password") {
    return i18n("componentNameEditPassword");
  } else if (props.type === "deviceName") {
    return i18n("componentNameEditDeviceName");
  } else {
    return "";
  }
});

watch(
  () => props.show,
  () => {
    formModel.value = props.form;
  },
);
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>