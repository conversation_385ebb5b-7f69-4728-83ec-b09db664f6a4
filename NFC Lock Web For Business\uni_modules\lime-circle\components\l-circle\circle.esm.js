function t(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function e(t,e){for(var i=0;e.length>i;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function i(t,i,n){return i&&e(t.prototype,i),n&&e(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function n(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==i)return;var n,r,a=[],o=!0,s=!1;try{for(i=i.call(t);!(o=(n=i.next()).done)&&(a.push(n.value),!e||a.length!==e);o=!0);}catch(t){s=!0,r=t}finally{try{o||null==i.return||i.return()}finally{if(s)throw r}}return a}(t,e)||r(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(t,e){if(t){if("string"==typeof t)return a(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?a(t,e):void 0}}function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);e>i;i++)n[i]=t[i];return n}var o=function(t){return/^#.{3,6}$/.test(t)?4===t.length?t.substring(1).split("").map((function(t){return 17*parseInt(t,16)})):[t.substring(1,3),t.substring(3,5),t.substring(5,7)].map((function(t){return parseInt(t,16)})):(console.error("lime-circle: 渐变仅支持hex值"),[0,0,0])},s=function(t){return 1===t.length?"0"+t:t},u=function(t,e,i){var n,r,a,u,h=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,c=[],l=[],f=function(t){return Math.pow(t/255,h)};for(t=o(t).map(f),e=o(e).map(f),n=0;i>n;n++){for(u=1-(a=n/(i-1)),r=0;3>r;r++)l[r]=s(Math.round(255*Math.pow(t[r]*u+e[r]*a,1/h)).toString(16));c.push("#"+l.join(""))}return c};var h=function(t,e,i,n){var r=1e-6,a=3*t-3*i+1,o=3*i-6*t,s=3*t,u=3*e-3*n+1,h=3*n-6*e,c=3*e;function l(t){return((a*t+o)*t+s)*t}return function(t){return e=function(t){for(var e,i,n,u=t,h=0;8>h;h++){if(i=l(u)-t,r>Math.abs(i))return u;if(r>Math.abs(e=(3*a*(n=u)+2*o)*n+s))break;u-=i/e}var c=1,f=0;for(u=t;c>f;){if(i=l(u)-t,r>Math.abs(i))return u;i>0?c=u:f=u,u=(c+f)/2}return u}(t),((u*e+h)*e+c)*e;var e}}(.25,.1,.25,1),c=Symbol("tick"),l=Symbol("tick-handler"),f=Symbol("animations"),d=Symbol("start-times"),v=Symbol("pause-start"),m=Symbol("pause-time"),y="undefined"!=typeof requestAnimationFrame?requestAnimationFrame:function(t){return setTimeout(t,1e3/60)},g="undefined"!=typeof cancelAnimationFrame?cancelAnimationFrame:function(t){clearTimeout(t)},p=function(){function e(){t(this,e),this.state=void 0,this.state="Initiated",this[f]=new Set,this[d]=new Map}return i(e,[{key:"start",value:function(){var t=this;if("Initiated"===this.state){this.state="Started";var e=Date.now();this[m]=0,this[c]=function(){var i,n=Date.now(),a=function(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=r(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,a=function(){};return{s:a,n:function(){return t.length>n?{done:!1,value:t[n++]}:{done:!0}},e:function(t){throw t},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,u=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return s=t.done,t},e:function(t){u=!0,o=t},f:function(){try{s||null==i.return||i.return()}finally{if(u)throw o}}}}(t[f]);try{for(a.s();!(i=a.n()).done;){var o=i.value,s=void 0;(s=t[d].get(o)<e?n-e-o.delay-t[m]:n-t[d].get(o)-o.delay-t[m])>o.duration&&(t[f].delete(o),s=o.duration),s>0&&o.run(s)}}catch(t){a.e(t)}finally{a.f()}t[l]=y(t[c])},this[c]()}}},{key:"pause",value:function(){"Started"===this.state&&(this.state="Paused",this[v]=Date.now(),g(this[l]))}},{key:"resume",value:function(){"Paused"===this.state&&(this.state="Started",this[m]+=Date.now()-this[v],this[c]())}},{key:"reset",value:function(){this.pause(),this.state="Initiated",this[m]=0,this[v]=0,this[f]=new Set,this[d]=new Map,this[l]=null}},{key:"add",value:function(t,e){2>arguments.length&&(e=Date.now()),this[f].add(t),this[d].set(t,e)}}]),e}(),w=function(){function e(i,n,r,a,o,s){t(this,e),this.startValue=void 0,this.endValue=void 0,this.duration=void 0,this.timingFunction=void 0,this.delay=void 0,this.template=void 0,o=o||function(t){return t},s=s||function(t){return t},this.startValue=i,this.endValue=n,this.duration=r,this.timingFunction=o,this.delay=a,this.template=s}return i(e,[{key:"run",value:function(t){var e=this.endValue-this.startValue,i=this.timingFunction(t/this.duration);this.template(this.startValue+e*i)}}]),e}(),b=Math.PI/180,A=function(){function e(i,n){t(this,e),this.canvas=void 0,this.context=void 0,this.current=0,this.size=0,this.pixelRatio=1,this._isConicGradient=!1,this._attrs={percent:0,size:120,lineCap:"round",strokeWidth:6,strokeColor:"#2db7f5",trailWidth:6,trailColor:"#ddd",dashboard:!1,clockwise:!0,duration:300,max:100,beforeAnimate:!0,animate:!0,formatter:"{d}{d}.{d}{d}%",fontSize:"16px",showText:!1,gapDegree:90,gapPosition:"bottom"},this._timer=void 0,this.startTime=0,this.target=0,this._colors=[],this._gradientColors=[],this._rAF=function(t){},this._cAf=function(t){},this.timeline=void 0,this.run=void 0,this.canvas=i,this.run=n.run,this.size=n.size||120,this.pixelRatio=n.pixelRatio||1,this.init(),this.initRaf(),this.timeline=new p}return i(e,[{key:"init",value:function(){var t=this.size,e=this.pixelRatio;if(this.canvas){this.canvas.width=t*e,this.canvas.height=t*e;var i=this.canvas.getContext("2d");this._isConicGradient=!!i.createConicGradient,this.context=i}}},{key:"initRaf",value:function(){var t=this.canvas;"undefined"!=typeof window?(this._rAF=window.requestAnimationFrame||function(t){return window.setTimeout(t,1e3/60)},this._cAf=window.cancelAnimationFrame||function(t){window.clearTimeout(t)}):t&&t.requestAnimationFrame?(this._rAF=t.requestAnimationFrame,this._cAf=t.cancelAnimationFrame):(this._rAF=function(t){return setTimeout(t,16.7)},this._cAf=function(t){clearTimeout(t)})}},{key:"setOption",value:function(t){Object.assign(this._attrs,t)}},{key:"set",value:function(t,e){this._attrs[t]=e}},{key:"get",value:function(t){return this._attrs[t]||this.canvas[t]}},{key:"play",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.get("percent");this.target=Math.max(Math.min(e,this.get("max")||100),0),this.createConicGradient(),this.timeline.start(),this.timeline.add(new w(this.current,e,this.get("duration"),0,h,(function(e){t.current=1e-4>e?0:e,t.render(t.current),t.run(t.current)})))}},{key:"createConicGradient",value:function(){if(!this._isConicGradient){var t=this._attrs.strokeColor;if("string"!=typeof t&&this._colors!=t&&Array.isArray(t)){var e=n(this.getDeg(),2),i=e[0],r=e[1];this._colors=t,this._gradientColors=[];for(var a=r-i,o=t.length-1,s=Math.floor(a/o),h=0;o>h;h++){a-=s,this._gradientColors=this._gradientColors.concat(u(t[h],t[h+1],h+1===o?s+a:s,1))}}}}},{key:"render",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=this.context,i=this.size,n=this.pixelRatio,r=this.getSalce(),a=i/2;e.setTransform(1,0,0,1,0,0),e.clearRect(2*-a,2*-a,4*i,4*i),e.setTransform(r*n,0,0,n,a*n,a*n),this.drawTrail(a),this.drawStroke(a,t),e.draw&&e.draw()}},{key:"drawArc",value:function(t,e,i,n,r){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:this._attrs.lineCap,o=this.context;o.beginPath(),o.lineCap=a,o.strokeStyle=e,o.lineWidth=i,o.arc(0,0,t,n,r),o.stroke()}},{key:"getSalce",value:function(){return this.get("clockwise")?1:-1}},{key:"getDeg",value:function(){var t=this._attrs,e=t.gapDegree;t.dashboard||(e=0);var i=(0===e?0:{bottom:0,top:180,left:90,right:-90}[t.gapPosition])+(e>0?90+e/2:-90)+0;return[i,i+360*((360-e)/360)]}},{key:"drawTrail",value:function(t){var e=this._attrs,i=e.trailWidth,r=e.trailColor,a=t-i/2,o=n(this.getDeg(),2);this.drawArc(a,r,i,o[0]*b,o[1]*b)}},{key:"drawStroke",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(e){var i=this._attrs,r=i.strokeWidth,a=i.strokeColor,o=i.max,s=i.dashboard,u=i.lineCap,h=this.getDeg(),c=n(h,2),l=c[0],f=c[1],d=t-r/2,v=Math.round((f-l)/o*e);if("string"==typeof a||this._isConicGradient)if(Array.isArray(a)&&this._isConicGradient){var m=this.context,y=m.createConicGradient(l,0,0);a.forEach((function(t,e){y.addColorStop(e/(a.length-1),t)})),this.drawArc(d,y,r,l*b,(v+l)*b)}else this.drawArc(d,a,r,l*b,(v+l)*b);else for(var g=0;v>g;g++)this.drawArc(d,this._gradientColors[g],r,(g+l)*b,(g+1+l)*b,s||f!=v+l?u:"butt")}}}]),e}();export{A as Circle};
