import { i18n } from "@/locales/_i18n.js";

export class Request {
  #url = "";
  #method = Request.methods.GET;
  #query = [];
  #data = {};
  #contentType = Request.contentTypes.DEFAULT;
  #token = "";
  #req = null;

  static baseUrl = "";

  static methods = {
    GET: "GET",
    POST: "POST",
    PUT: "PUT",
    DELETE: "DELETE",
  };

  static contentTypes = {
    DEFAULT: "application/x-www-form-urlencoded",
    JSON: "application/json",
  };

  constructor(url) {
    this.url(url);
  }

  url(url) {
    this.#url = url;
    return this;
  }

  method(method) {
    this.#method = method;
    return this;
  }

  query(query) {
    this.#query = query;
    return this;
  }

  data(data) {
    this.#data = data;
    return this;
  }

  contentType(contentType) {
    this.#contentType = contentType;
    return this;
  }

  token(token) {
    this.#token = token;
    return this;
  }

  send() {
    const url = `${Request.baseUrl}${this.#url}/${this.#query.join("/")}`;
    const header = {
      "content-type": this.#contentType,
    };
    if (this.#token) {
      header.Authorization = this.#token;
    }
    this.#req = new Promise((resolve) => {
      uni.request({
        url,
        method: this.#method,
        data: this.#data,
        header,
        dataType: "json",
        success(res) {
          resolve([true, res]);
        },
        fail(err) {
          resolve([false, err]);
        },
      });
    });
  }

  async getData() {
    if (this.#req) {
      const [status, result] = await this.#req;
      if (status) {
        return result.data;
      } else {
        throw new Error(i18n.global.tm("modalContentRequestFailed"));
      }
    } else {
      throw new Error("Request not sent");
    }
  }
}
