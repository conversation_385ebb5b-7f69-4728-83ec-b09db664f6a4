.page {
  width: 100%;
  height: 100%;
  
  .content-box {
    height: 100%;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
  
    .device-info {
      background-color: #3080F7;
      border-top-left-radius: 12rpx;
      border-top-right-radius: 12rpx;
      padding: 28rpx;
      margin-top: 30rpx;
      color: #ffffff;
      box-shadow: 0rpx 0rpx 20rpx #3080F766;
  
      .wrap {
        display: flex;
        margin-top: 8rpx;
        
        &:first-child {
          margin-top: 0rpx;
        }
  
        .title {
          font-weight: bold;
          width: 130rpx;
          text-align-last: justify;
        }
  
        .text {
          flex: 1;
          margin-left: 20rpx;
        }
      }
    }
  
    .form {
      background-color: #ffffff;
      color: #333;
      padding: 0rpx 28rpx;
      box-shadow: 0rpx 0rpx 40rpx #3080F722;
      border-bottom-left-radius: 12rpx;
      border-bottom-right-radius: 12rpx;
      padding-bottom: 28rpx;
  
      .form-item {
        width: 100%;
        height: 100rpx;
        display: flex;
        align-items: center;
        border-bottom: 2rpx solid #eeeeee;
  
        .label {
          font-weight: bold;
          width: 130rpx;
          text-align-last: justify;
        }
  
        .value {
          flex: 1;
          margin: 0rpx 20rpx;
          color: #888888;
        }
      }
    }
  
    .btn-submit {
      width: 100%;
      height: 80rpx;
      color: white;
      background-color: #3080F7;
      border-radius: 12rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      padding: 0rpx 30rpx;
      box-shadow: 0rpx 0rpx 20rpx #3080F766;
      margin-top: 40rpx;
    }
  }
}