.page {
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, #3080F711, #f6f6f6);
  
  .content-box {
    display: grid;
    flex-direction: column;
    padding: 20rpx 0rpx;
    grid-template-rows: 160rpx auto 160rpx 160rpx 160rpx;
    grid-template-columns: 1fr 1fr;
    row-gap: 36rpx;
    column-gap: 24rpx;
    
    .grid-item-primary {
      padding: 12rpx 32rpx;
      background: linear-gradient(to right, #3080F7, #b5b3fc);
      border-radius: 12rpx;
      box-shadow: 0rpx 0rpx 20rpx #3080F766;
      display: flex;
      justify-content: space-around;
      flex-direction: column;
      
      .wrap-1 {
        display: flex;
        align-items: center;
        
        .icon {
          width: 50rpx;
          height: 50rpx;
          background-size: contain;
          background-position: center center;
          background-repeat: no-repeat;
        }
        
        .text {
          margin-left: 12rpx;
          color: #ffffff;
        }
      }
        
      .number {
        font-size: 52rpx;
        color: #ffffff;
        font-weight: bold;
      }
    }
  
    .device-number {
      grid-row: 1;
      grid-column: 1;
  
      .wrap-1 {
        .icon {
          background-image: url("../../static/new_lock_close_white.png");
        }
      }
    }
  
    .unlock-count {
      grid-row: 1;
      grid-column: 2;

      .wrap-1 {
        .icon {
          background-image: url("../../static/new_lock_open_white.png");
        }
      }
    }
  
    .video {
      grid-row: 2;
      grid-column: 1 / span 2;
      
      .video-content {
        width: 100%;
      }
    }
    
    .grid-item {
      padding: 20rpx;
      background-color: #ffffff;
      border-radius: 12rpx;
      box-shadow: 0rpx 0rpx 40rpx #3080F722;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      
      .icon {
        width: 110rpx;
        height: 110rpx;
        background-size: contain;
        background-position: center center;
        background-repeat: no-repeat;
      }
        
      .text {
        font-weight: bold;
      }
    }
  
    .instruction {
      .icon {
        background-image: url("../../static/new_instruction_primary.png");
      }
    }
  
    .device-manage {
      .icon {
        background-image: url("../../static/new_device_manage_primary.png");
      }
    }
  
    .operation-log {
      .icon {
        background-image: url("../../static/new_log_primary.png");
      }
    }
  
    .alert-info {
      .icon {
        background-image: url("../../static/new_alert_primary.png");
      }
    }
  }
}