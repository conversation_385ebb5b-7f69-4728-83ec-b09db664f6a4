.page {
  width: 100%;
  height: 100%;
  
  .content-box {
    height: 100%;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    padding: 20rpx 0rpx;
  
    .device-info {
      background-color: #3080F7;
      border-radius: 12rpx;
      padding: 28rpx;
      color: #ffffff;
      box-shadow: 0rpx 0rpx 20rpx #3080F766;

      &[type="normal"] {
        background-color: #3080F7;
      }

      &[type="success"] {
        background-color: #8bc34a;
      }

      &[type="error"] {
        background-color: #e74c3c;
      }
  
      .wrap {
        display: flex;
        align-items: center;
  
        .title {
          font-weight: bold;
          width: 130rpx;
          text-align-last: justify;
        }
  
        .text {
          flex: 1;
          margin-left: 20rpx;
        }
      }
    }
  
    .btn-group {
      margin-top: 30rpx;
      display: flex;

      .btn {
        height: 80rpx;
        color: #3080F7;
        background-color: #ffffff;
        border-radius: 12rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        flex: 1;
        padding: 0rpx 30rpx;
        box-shadow: 0rpx 0rpx 20rpx #3080F766;

        &[selected="true"] {
          background-color: #3080F7;
          color: #ffffff;
        }
      }

      .btn-batch {
        margin-left: 30rpx;
      }
    }
  
    .wrap-middle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 30rpx 0rpx;
  
      .text {
        font-size: 110%;
        font-weight: bold;
      }

      .wrap-filter {
        display: flex;
        align-items: center;

        .filter {
          color: #888888;
          margin-right: 10rpx;
        }
      }
    }

    .empty-box {
      height: 0rpx;
      flex: 1;
    }
  
    .device-list {
      height: 0rpx;
      flex: 1;
  
      .list-item {
        display: flex;
        align-items: center;
        padding: 20rpx 0rpx;
        border-bottom: 2rpx solid #eeeeee;
        
        &[selected="true"] {
          .name {
            color: #3080F7;
          }
        }
  
        .infos {
          flex: 1;
  
          .wrap-1 {
            display: flex;
            justify-content: space-between;
            align-items: center;
  
            .name {
              font-weight: bold;
              display: flex;
              align-items: center;
            }
  
            .status {
              color: #ffffff;
              background-color: #3080F7;
              font-size: 85%;
              padding: 6rpx 16rpx;
              border-radius: 2000rpx;

              &[type="normal"] {
                background-color: #3080F7;
              }

              &[type="success"] {
                background-color: #8bc34a;
              }

              &[type="error"] {
                background-color: #e74c3c;
              }

              &[type="unknown"] {
                background-color: #888888;
              }
            }
          }
  
          .device-id {
            color: #888888;
            font-size: 85%;
            margin-top: 4rpx;
          }
        }
      }
    }
  }
}