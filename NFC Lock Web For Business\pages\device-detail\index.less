.page {
  width: 100%;
  height: 100%;
  
  .content-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20rpx 0rpx;
  
    .device-info {
      background-color: #3080F7;
      border-radius: 12rpx;
      padding: 28rpx;
      color: #ffffff;
      box-shadow: 0rpx 0rpx 20rpx #3080F766;
  
      .wrap {
        display: flex;
        align-items: center;
  
        .title {
          font-weight: bold;
          width: 130rpx;
          text-align-last: justify;
        }
  
        .text {
          flex: 1;
          margin-left: 20rpx;
        }
      }
    }

    .picker {
      margin-top: 20rpx;
    }
  
    .title-sub {
      margin: 20rpx 0rpx;
      font-weight: bold;
    }

    .empty-box {
      height: 0rpx;
      flex: 1;
    }
  
    .device-record-list {
      flex: 1;
      position: relative;
      height: 0rpx;
      
      .list-wrap {
        position: absolute;
        width: calc(100% + 40rpx);
        height: 100%;
        top: 0;
        left: -20rpx;
        
        .list-item {
          background-color: #ffffff;
          padding: 12rpx 20rpx;
          border-radius: 12rpx;
          box-shadow: 0rpx 0rpx 40rpx #3080F722;
          margin: 0rpx 20rpx;
          margin-top: 14rpx;
          
          .wrap-1 {
            display: flex;
            justify-content: space-between;
            align-items: center;
          
            .name {
              font-weight: bold;
            }
          
            .status {
              color: #ffffff;
              background-color: #3080F7;
              font-size: 85%;
              padding: 6rpx 16rpx;
              border-radius: 2000rpx;

              &.success {
                background-color: #8bc34a;
              }

              &.error {
                background-color: #ff5252;
              }
            }
          }
          
          .wrap-2 {
            margin-top: 8rpx;
            color: #888888;
            font-size: 85%;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          
          .wrap-3 {
            margin-top: 8rpx;
            font-size: 85%;
            display: flex;
            align-items: center;
            
            .btn {
              padding: 5rpx 8px;
              margin-right: 14rpx;
              color: white;
              border-radius: 99rpx;

              &.blue {
                background-color: #3080F7;
              }
              
              &.green {
                background-color: #8bc34a;
              }
            }
          }
        }
      }
    }
  }
}