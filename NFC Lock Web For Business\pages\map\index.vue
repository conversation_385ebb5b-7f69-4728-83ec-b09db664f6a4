<template>
  <view class="page">
    <base-layout
      page-title="地图"
      :show-back="true"
      :show-tab="false"
      @user-info="(info) => (userInfo = info)"
    >
      <template #content>
        <view class="map-box">
          <map
            class="map"
            :latitude="latitude"
            :longitude="longitude"
            :markers="markers"
            :scale="scale"
          />
        </view>
      </template>
    </base-layout>
  </view>
</template>

<script setup>
import { ref } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { uniModal } from "@/utils/util.js";

import BaseLayout from "@/components/base-layout/index.vue";
import marker from "@/static/marker.png";

const latitude = ref(0);
const longitude = ref(0);
const scale = ref(18);
const iconPath = ref(marker);
const markers = ref([]);

onLoad((options) => {
  latitude.value = options.lat;
  longitude.value = options.lng;
  if (options.scale) {
    scale.value = options.scale;
  }

  if (latitude.value && longitude.value && isFinite(latitude.value) && isFinite(longitude.value)) {
    uni.request({
      url: `http://api.map.baidu.com/ag/coord/convert?from=0&to=2&x=${longitude.value}&y=${latitude.value}`,
      method: "GET",
      success: (res) => {
        latitude.value = atob(res.data.y);
        longitude.value = atob(res.data.x);
        markers.value = [
          {
            id: 1,
            latitude: latitude.value,
            longitude: longitude.value,
            iconPath: iconPath.value,
            width: 24,
            height: 24,
          },
        ];
      },
    });
  } else {
    uniModal("没有经纬度信息或经纬度信息错误", false, () => {
      uni.navigateBack({
        delta: 1,
      });
    });
  }
});

onShow(() => {

});
</script>

<style lang="less" scoped>
@import "./index.less";
</style>