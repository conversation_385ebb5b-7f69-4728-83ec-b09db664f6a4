<template>
	<view class="demo-block">
		<text class="demo-block__title-text ultra">进度球</text>
		<text class="demo-block__desc-text">水波进度球</text>	
		<view class="demo-block__body">
			<view class="demo-block">
				<text class="demo-block__title-text large">基础</text>
				<view class="demo-block__body">
					<l-liquid class="bg" :percent="target1" v-model:current="modelVale1" >
						<text class="text">{{modelVale1}}<text class="unit">%</text></text>
					</l-liquid>
				</view>	
			</view>	
			<view class="demo-block">
				<text class="demo-block__title-text large">外框</text>
				<view class="demo-block__body">
					<l-liquid class="bg" :percent="target1" v-model:current="modelVale1" :outline="true">
						<text class="text">{{modelVale1}}<text class="unit">%</text></text>
					</l-liquid>
				</view>	
			</view>	
		</view>	
		<button @tap="onClick(20)">增加</button>
		<button style="margin-top: 10rpx;" @tap="onClick(-20)">减少</button>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				target1: 50,
				modelVale1: 0,
			}
		},
		methods: {
			onClick(number: number) {
				this.target1 = Math.max(Math.min(100, this.target1 + number), 0)
			}
		}
	}
</script>
<style lang="scss">
	.demo-block {
		margin: 32px 20px 0;
		&__title {
			margin: 0;
			margin-top: 8px;
			&-text {
				color: rgba(0, 0, 0, 0.6);
				font-weight: 400;
				font-size: 14px;
				line-height: 16px;
				
				&.large {
					color: rgba(0, 0, 0, 0.9);
					font-size: 18px;
					font-weight: 700;
					line-height: 26px;
				}
				
				&.ultra {
					color: rgba(0, 0, 0, 0.9);
					font-size: 24px;
					font-weight: 700;
					line-height: 32px;
				}
			}
		}
		&__desc-text {
			color: rgba(0, 0, 0, 0.6);
			margin: 8px 16px 0 0;
			font-size: 14px;
			line-height: 22px;
		}
		&__body {
			margin: 16px 0;
			.demo-block {
				// margin-top: 0px;
				margin: 0;
			}
		}
	}
</style>
