.list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .empty {
    height: 100%;
  }
  
  .pagination {
    padding: 20rpx 0rpx;
  }
  
  .selection-btns {
    :nth-child(n) {
      margin-top: 16rpx;
      padding: 26rpx 0rpx;
      color: white;
      background-color: #007bff;
      border: 2rpx solid #007bff;
      border-radius: 99rpx;
      display: flex;
      justify-content: center;
    }
  }
  
  .list-items {
    flex: 1;
    height: 0;
    
    .flow-root {
      display: flow-root;
    
      .list-item {
        padding: 20rpx 10rpx;
        border-bottom: 2rpx solid #eee;
        
        &.list {
          display: flex;
          align-items: center;
          
          .info-box {
            flex: 1;
            width: 0;
            word-break: break-all;
          }
          
          .btn-box {
            margin-left: 20rpx;
            display: flex;
          }
          
          .selection {
            margin-left: 20rpx;
          }
        }
        
        &.card {
          background-color: white;
          padding: 32rpx;
          margin-top: 20rpx;
          box-shadow: 0px 4px 15px 0px rgba(0,0,0,0.03);
          border: none;
          border-radius: 14rpx;
          
          .info-box {
            padding-bottom: 32rpx;
            word-break: break-all;
            border-bottom: 2rpx solid #eee;
            
            .title {
              font-size: 30rpx;
              font-weight: bold;
            }
            
            .title-sub {
              margin-top: 12rpx;
              color: gray;
              font-size: 24rpx;
              white-space: pre-wrap;
            }
          }
          
          .btn-box {
            margin-top: 32rpx;
            display: flex;
            justify-content: flex-end;
          }
        }
        
        .info-box {
          .title {
            font-weight: bold;
          }
          
          .title-sub {
            margin-top: 4rpx;
            color: gray;
            font-size: 80%;
            white-space: pre-wrap;
          }
        }
        
        .btn-box {
          :nth-child(n) {
            padding: 10rpx 24rpx;
            margin-left: 12rpx;
            color: white;
            font-size: 80%;
            border-radius: 99rpx;
          }
          
          .share {
            background-color: #2977FF;
          }
          
          .record {
            color: #2977FF;
            border: 2rpx solid #2977FF;
          }
          
          .edit {
            color: #666666;
            border: 2rpx solid #666666;
          }
          
          .delete {
            color: #f40928;
            border: 2rpx solid #f40928;
          }
        }
      }
    }
  }
}