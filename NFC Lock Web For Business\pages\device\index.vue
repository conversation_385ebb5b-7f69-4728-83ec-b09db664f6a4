<template>
  <view class="page">
    <base-layout
      page-title="设备"
      :selected-index="1"
      @user-info="(info) => userInfo = info"
    >
      <template #content>
        <view class="content-box">
          <view class="search-bar">
            <uv-search
              v-model="searchContent"
              placeholder="搜索设备名称"
              :show-action="false"
              :animation="false"
              @change="onSearch"
            />
          </view>
          <scroll-view v-if="deviceList.length" scroll-y class="device-list" @scrolltolower="onDeviceListToLower">
            <view
              v-for="(item, index) in deviceList"
              :key="index"
              class="list-item"
              @click="toDeviceDetail(item.id)"
            >
              <view class="icon-box">
                <view class="icon" />
              </view>
              <view class="infos">
                <view class="wrap-1">
                  <view class="name">
                    {{ item.name }}
                  </view>
                  <view class="status" :status="deviceStatus[item.action_type]?.status || 'unknown'">
                    {{ deviceStatus[item.action_type]?.text || "未知状态" }}
                  </view>
                </view>
                <view class="address">
                  {{ item.addr }}
                </view>
              </view>
            </view>
          </scroll-view>
          <uv-empty v-else class="empty-box" name="list" icon-size="110" text-size="15" text="暂无数据" />
        </view>
      </template>
    </base-layout>
  </view>
</template>

<script setup>
import { ref, watch } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { DeviceListApi } from "@/utils/api.js";
import { deviceStatus } from "./data.js";

import BaseLayout from "@/components/base-layout/index.vue";

const userInfo = ref({});
const searchContent = ref(null);
const searchTimeout = ref(null);
const deviceList = ref([]);
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);

const initDatas = async () => {
  const api = new DeviceListApi().token(userInfo.value.token).data({
    name: searchContent.value || "",
    page: currentPage.value,
    page_size: pageSize.value,
  });
  await api.send();
  const result = await api.getData();
  const list = result.data?.list || [];
  deviceList.value = [...deviceList.value, ...list];
  total.value = result.data.total;
};

const onSearch = () => {
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }
  searchTimeout.value = setTimeout(() => {
    currentPage.value = 1;
    deviceList.value = [];
    initDatas();
  }, 1000);
};

const onDeviceListToLower = () => {
  if (deviceList.value.length < total.value) {
    currentPage.value += 1;
    initDatas();
  }
};

const toDeviceDetail = (id) => {
  uni.navigateTo({
    url: `/pages/device-detail/index?id=${id}`,
  });
};

watch(() => userInfo.value, () => {
  initDatas();
});

onLoad(() => {

});

onShow(() => {

});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>