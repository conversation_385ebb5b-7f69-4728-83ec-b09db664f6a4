{"name": "nfc-lock", "version": "1.0.0", "description": "nfc lock", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint --config .eslintrc.js --ext .vue,.js ./ --report-unused-disable-directives --fix"}, "keywords": ["nfc"], "author": "", "license": "ISC", "dependencies": {"vue-i18n": "^9.14.0"}, "devDependencies": {"eslint": "^8.26.0", "eslint-plugin-html": "^8.1.3", "eslint-plugin-vue": "^9.32.0", "vue-eslint-parser": "^9.4.3"}}