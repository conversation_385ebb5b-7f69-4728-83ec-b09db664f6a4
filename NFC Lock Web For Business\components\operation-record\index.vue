<template>
  <my-drawer
    :title="i18n('componentNameOperationRecord')"
    :height="-1"
    :open="show"
    @close="onBack"
  >
    <template #content>
      <view class="container">
        <view class="picker">
          <uni-datetime-picker
            v-model="selectedDates"
            :border="false"
            type="daterange"
            :start-placeholder="i18n('placeholderStartDate')"
            :end-placeholder="i18n('placeholderEndDate')"
            :range-separator="i18n('keywordTo')"
            @change="onDateChange"
          />
        </view>
        <template v-if="recordDatas.length">
          <view class="list-box">
            <my-list
              ref="mylist"
              :datas="recordDatas"
              :page-size="pageSize"
              :total="pageTotal"
              :has-pagination="false"
              :empty="i18n('defaultEmpty')"
              @page-change="onPageChange"
              @scrolltolower="onScrollToLower"
            />
          </view>
        </template>
        <empty
          v-else
          mode="list"
        />
      </view>
    </template>
  </my-drawer>
</template>

<script setup>
import { reactive, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { RecordListApi } from "@/utils/api.js";

import MyDrawer from "../drawer/index.vue";
import MyList from "../list/index.vue";
import Empty from "../empty/index.vue";

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  sid: {
    type: Number,
    default: -1,
  },
  info: {
    type: Object,
    default: () => ({}),
  },
});

const emits = defineEmits(["update:show"]);

const { tm: i18n } = useI18n();
const mylist = ref(null);
const selectedDates = ref([]);
const currentPage = ref(1);
const pageSize = 15;
const pageTotal = ref(0);
const recordDatas = ref([]);

const onInit = async () => {
  if (props.info) {
    const api = new RecordListApi().token(props.info.token).data({
      device_id: props.sid,
      date_gte: selectedDates.value[0],
      date_lte: selectedDates.value[1],
      page: currentPage.value,
      size: pageSize,
    });
    await api.send();
    const result = await api.getData();
    recordDatas.value = [
      ...recordDatas.value,
      ...result.data.list.map((item) => ({
        title: item.username,
        titleSub: `${i18n("keywordOperationType")}：${[i18n("btnUnlock"), i18n("btnLock")][item.is_type]}\n${i18n("keywordOperationDate")}：${item.created_at}`,
      })),
    ];
    pageTotal.value = result.data.total;
  }
};

const onPageChange = (current) => {
  currentPage.value = current;
  onInit();
};

const onScrollToLower = () => {
  if (currentPage.value < Math.ceil(pageTotal.value / pageSize)) {
    onPageChange(currentPage.value + 1);
  }
};

const onDateChange = async (dates) => {
  selectedDates.value = dates;
  currentPage.value = 1;
  recordDatas.value = [];
  await onInit();
  mylist.value?.setPage?.(1);
};

const onBack = () => {
  emits("update:show", false);
};

watch(() => props.show, () => {
  if (props.show) {
    selectedDates.value = [];
    onInit();
  }
});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>