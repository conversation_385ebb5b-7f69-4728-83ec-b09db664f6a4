<template>
  <view class="page">
    <base-layout
      page-title="操作日志"
      :show-back="true"
      :show-tab="false"
      @user-info="(info) => userInfo = info"
    >
      <template #content>
        <view class="content-box">
          <view class="picker">
            <uni-datetime-picker
              v-model="selectedDates"
              :border="false"
              type="daterange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              range-separator="至"
              @change="onDateChange"
            />
          </view>

          <scroll-view v-if="operationLogList.length" scroll-y class="list-box" @scrolltolower="onOperationLogListToLower">
            <view
              v-for="(item, index) in operationLogList"
              :key="index"
              class="list-item"
            >
              <view class="device-name">
                {{ item.device_name || "未知设备" }}
              </view>
              <view class="wrap-1">
                <view class="name">
                  {{ item.username }}
                </view>
                <view class="status" :class="deviceOpenStatus[item.is_type]?.status">
                  {{ deviceOpenStatus[item.is_type]?.text }}
                </view>
              </view>
              <view class="wrap-2">
                <view class="datetime">
                  {{ item.created_at }}
                </view>
                <view class="remark">
                  偏移 {{ item.offset }} 米
                </view>
              </view>
              <view class="wrap-3">
                <div v-if="item.pic" class="btn green" @click="previewPhoto(item)">
                  查看照片
                </div>
                <div v-else class="btn blue" @click="uploadPhoto(item)">
                  上传照片
                </div>
                <div class="btn green" @click="toMap(item)">
                  查看位置
                </div>
              </view>
            </view>
            <br>
          </scroll-view>
          <uv-empty v-else class="empty-box" name="list" icon-size="110" text-size="15" text="暂无数据" />
        </view>
      </template>
    </base-layout>
  </view>
</template>

<script setup>
import { ref, watch } from "vue";
import { onLoad, onShow, onHide, onUnload } from "@dcloudio/uni-app";
import { OperationLogListApi, UploadUnlockPhotoApi } from "@/utils/api.js";
import { deviceOpenStatus} from "@/pages/device/data.js";

import BaseLayout from "@/components/base-layout/index.vue";

const userInfo = ref({});
const operationLogList = ref([]);
const currentPage = ref(1);
const pageSize = ref(50);
const total = ref(0);
const selectedDates = ref([]);
const selectedId = ref(-1);

const initDatas = async () => {
  const data = {
    start_time: selectedDates.value[0],
    end_time: selectedDates.value[1],
    page: currentPage.value,
    page_size: pageSize.value,
  };
  if (userInfo.value.role_id !== 1) {
    data.user_id = userInfo.value.id;
  }
  const api = new OperationLogListApi().token(userInfo.value.token).data(data);
  await api.send();
  const result = await api.getData();
  operationLogList.value = [...operationLogList.value, ...result.data.list];
  total.value = result.data.total;
};

const onOperationLogListToLower = () => {
  if (operationLogList.value.length < total.value) {
    currentPage.value += 1;
    initDatas();
  }
};

const onDateChange = (e) => {
  selectedDates.value = e;
  currentPage.value = 1;
  operationLogList.value = [];
  initDatas();
};

const uploadPhoto = (item) => {
  selectedId.value = item.id;
  window.android?.capture?.();
};

const previewPhoto = (item) => {
  uni.previewImage({
    urls: [`${UploadUnlockPhotoApi.baseUrl}${item.pic}`],
  });
};

const toMap = (item) => {
  uni.navigateTo({
    url: `/pages/map/index?lat=${item.lt}&lng=${item.lg}`,
  });
};

watch(() => userInfo.value, () => {
  initDatas();
});

onLoad(() => {
  window.eventCallback = (name, v1) => {
    if (name === "capture") {
      if (selectedId.value > 0) {
        const api = new UploadUnlockPhotoApi().token(userInfo.value.token);
        api.upload(v1, async (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.code === 200) {
              api.data({
                id: selectedId.value,
                pic: data.data,
              });
              await api.send();
              const result = await api.getData();
              if (result.code === 200) {
                uniModal("照片上传成功");
                operationLogList.value = [];
                currentPage.value = 1;
                initOperationLogListDatas();
              } else {
                uniModal("照片上传失败");
              }
            }
          } catch(err) {
            uniModal("照片上传失败");
          }
        }, () => {
          uniModal("照片上传失败");
        });
      }
    }
  };
});

onShow(() => {

});

onHide(() => {
  window.eventCallback = () => null;
});

onUnload(() => {
  window.eventCallback = () => null;
});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>