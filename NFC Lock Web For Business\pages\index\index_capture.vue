<template>
  <view class="page">
    <base-layout page-title="操作" :selected-index="2" @user-info="(info) => userInfo = info">
      <template #content>
        <view class="content-box">
          <view class="spinner">
            <l-circle v-model:current="currentPercent" class="progress" :size="ringWidth" :duration="600" :trail-width="trailWidth" trail-color="#A4C5D5" :stroke-width="strokeWidth" :stroke-color="strokeColors" :percent="spinnerPercent" />
            <view class="circle">
              <view class="percent">
                {{ Number(currentPercent).toFixed(0) }}%
              </view>
            </view>
          </view>
          <view v-if="deviceId" class="down-box">
            <view class="device-infos">
              <text v-if="deviceName" class="device-name">
                设备名称：{{ deviceName }}
              </text>
              <text v-if="deviceId" class="device-id">
                设备ID：{{ deviceId }}
              </text>
              <text class="copy" @click="onCopy">
                复制设备信息
              </text>
            </view>
            <view class="btn-group">
              <view class="btn btn-unlock" :selected="operation === 'unlock'" @click="setOperation('unlock')">
                <text class="text">
                  开锁
                </text>
              </view>
              <view class="btn btn-lock" :selected="operation === 'lock'" @click="setOperation('lock')">
                <text class="text">
                  关锁
                </text>
              </view>
              <view class="btn btn-capture" :selected="operation === 'capture'" @click="toCapture">
                <text class="text">
                  拍照
                </text>
              </view>
              <view class="btn btn-record" :selected="operation === 'record'" @click="toRecord">
                <text class="text">
                  记录
                </text>
              </view>
            </view>
          </view>
          <view v-else class="down-box">
            <view class="detect-prompt">
              请贴合需要操作的设备
            </view>
          </view>
        </view>
      </template>
    </base-layout>
  </view>
</template>

<script setup>
import { computed, reactive, ref, watch } from "vue";
import { onLoad, onShow, onHide, onUnload } from "@dcloudio/uni-app";
import { uniModal } from "@/utils/util.js";
import { deviceOpenStatus } from "@/pages/device/data.js";
import { DeviceDetailByEntityIdApi, LocalDeviceListApi, OperationLogCreateApi, UploadUnlockPhotoApi } from "@/utils/api.js";
import Config from "@/config.json";

import BaseLayout from "@/components/base-layout/index.vue";
import value from "@/uni_modules/uv-text/components/uv-text/value";

const userInfo = ref({});
const availableDevices = ref([]);
const locationInfo = ref({});
const operation = ref("");
const step = ref(0);
const deviceId = ref("");
const deviceName = ref("");
const currentPercent = ref(0);
const spinnerPercent = ref(0);
const trailWidth = uni.upx2px(60);
const strokeWidth = uni.upx2px(60);
const ringWidth = uni.upx2px(355 + 60 * 2).toFixed(0);
const recordId = ref(-1);
const captureFlag = reactive([false, false]);

const strokeColors = computed(() => {
  if (currentPercent.value < 15) {
    return ["#5399D5", "#5399D5"];
  } else {
    return ["#5399D5", "#72D8D7", "#7EF0D8", "#549CD5", "#5399D5"];
  }
});

const setOperation = (value) => {
  const lastLocation = window.android?.getLastLocation?.();
  // if (lastLocation) {
  const [latitude, longitude] = lastLocation?.split?.(",") ?? [];
  locationInfo.value = {
    latitude,
    longitude,
  };
  if (operation.value === value) {
    operation.value = "";
  } else {
    operation.value = value;
  }
  window.android?.setMode?.("normal");
  window.android?.setAction?.(Config.operationName[operation.value]);
  // } else {
  // uniModal("定位数据获取失败，请检查定位功能是否开启");
  // }
};

const setState = (_step, _percent) => {
  step.value = _step;
  spinnerPercent.value = _percent;
};

const onDeviceNotFound = async (deviceId) => {
  const api = new DeviceDetailByEntityIdApi().token(userInfo.value.token).data({
    entity_id: deviceId,
  });
  await api.send();
  const result = await api.getData();
  if (result.data?.id) {
    uniModal("该设备未授权，是否向管理员申请设备权限？", true, (res) => {
      if (res.confirm) {
        uni.navigateTo({
          url: `/pages/device-require/index?id=${result.data.id}`,
        });
      }
    });
  } else {
    uniModal("该设备不存在");
  }
};

const onWrongDeviceInfo = () => {
  uniModal("设备信息错误");
};

const onLockOrUnlockSuccess = async (operation) => {
  const api = new OperationLogCreateApi().token(userInfo.value.token).data({
    user_id: userInfo.value.id,
    username: userInfo.value.username,
    phone: userInfo.value.phone,
    device_id: availableDevices.value.find((device) => device.entity_id === deviceId.value)?.id,
    is_type: Object.entries(deviceOpenStatus).find(([, value]) => value.name === operation)?.[0],
    lg: locationInfo.value.longitude || "",
    lt: locationInfo.value.latitude || "",
  });
  await api.send();
  const result = await api.getData();
  if (result.code === 200) {
    if (operation === "lock") {
      recordId.value = result.data;
      captureFlag[0] = true;
    }
  } else {
    uniModal("操作记录上传失败");
  }
};

const setCallbacks = () => {
  window.actionCallback = (name, v1, v2) => {
    if (name === "start") {
      setState(0, 0);
      if (!deviceId.value) {
        window.android?.setAction?.("verify");
      }
    } else if (name === "detect") {
      if (v1) {
        if (!deviceId.value) {
          deviceName.value = availableDevices.value.find((device) => device.entity_id === v1)?.name || "";
          if (deviceName.value) {
            deviceId.value = v1;
          }
        }
        setState(0, 0);
        window.android?.setMode?.("normal");
        window.android?.setAction?.(Config.operationName[operation.value]);
      } else {
        // deviceId.value = "";
        // deviceName.value = "";
        if (operation.value && step.value < 3) {
          // connection lost
          // setState(3, 0);
        }
      }
    } else if (name === "info" && v1 === "not found") {
      onDeviceNotFound(v2);
      setState(3, 0);
    } else if (name === "info" && v1 === "wrong") {
      onWrongDeviceInfo();
      setState(3, 0);
    } else if (name === "on") {
      if (v1 === "charge") {
        setState(1, 60);
      } else if (v1 === Config.operationName.lock) {
        setState(2, 90);
        onLockOrUnlockSuccess("lock");
      } else if (v1 === Config.operationName.unlock) {
        setState(2, 90);
        onLockOrUnlockSuccess("unlock");
      }
    } else if (name === Config.operationName.lock) {
      if (v1 === "true") {
        setState(3, 100);
        setOperation("");
        captureFlag[1] = true;
      } else if (v1 === "false") {
        // setState(3, 0);
      }
    } else if (name === Config.operationName.unlock) {
      if (v1 === "true") {
        setState(3, 100);
        setOperation("");
      } else if (v1 === "false") {
        // setState(3, 0);
      }
    }
  };
};

const clearCallbacks = () => {
  window.actionCallback = () => {};
  window.eventCallback = () => {};
};

const initDatas = async () => {
  const api = new LocalDeviceListApi();
  await api.sync(userInfo.value.token, 0);
  const datas = api.load();
  availableDevices.value = datas;
};

const setLockInfos = () => {
  const lockInfos = availableDevices.value.map((device) => ({
    id: device.entity_id,
    key: device.password || "",
  }));
  window.android?.setLockInfos?.(JSON.stringify(lockInfos));
};

const onCopy = () => {
  const device = availableDevices.value.find((item) => item.entity_id === deviceId.value);
  if (device) {
    window.android?.copy?.(`设备名称: ${device.name}, 设备ID: ${device.entity_id}`);
    uni.showToast({
      title: "复制成功",
      duration: 2000,
    });
  }
};

const toCapture = () => {
  if (recordId.value > 0) {
    window.android?.capture?.();
  } else {
    uniModal("请先进行关锁操作");
  }
};

const toRecord = () => {
  const device = availableDevices.value.find((item) => item.entity_id === deviceId.value);
  if (device) {
    uni.navigateTo({
      url: `/pages/device-detail/index?id=${device.id}`,
    });
  }
};

watch(() => userInfo.value, async () => {
  await initDatas();
  setLockInfos();
});

watch(() => captureFlag, () => {
  if (captureFlag[0] && captureFlag[1]) {
    captureFlag[0] = false;
    captureFlag[1] = false;
    toCapture();
  }
}, {
  deep: true,
});

onLoad(() => {
  
});

onShow(() => {
  setCallbacks();
  window.eventCallback = (name, v1) => {
    setState(0, 0);
    if (name === "capture") {
      if (recordId.value > 0) {
        const api = new UploadUnlockPhotoApi().token(userInfo.value.token);
        api.upload(v1, async (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.code === 200) {
              api.data({
                id: recordId.value,
                pic: data.data,
              });
              await api.send();
              const result = await api.getData();
              if (result.code === 200) {
                uniModal("照片上传成功");
              } else {
                uniModal("照片上传失败");
              }
            }
          } catch(err) {
            uniModal("照片上传失败");
            console.error(err);
          }
        }, () => {
          uniModal("照片上传失败");
        });
      }
    }
  };
});

onHide(() => {
  clearCallbacks();
  window.android?.setMode?.("");
  window.android?.setAction?.("");
});

onUnload(() => {
  clearCallbacks();
  window.android?.setMode?.("");
  window.android?.setAction?.("");
});
</script>

<style lang="less" scoped>
  @import "./index_capture.less";

  :deep(.l-liquid--outline) {
    border-color: #3080F7;
  }
</style>