## uv-ui-tools 工具集

> **组件名：uv-ui-tools**

uv-ui工具集成，包括网络Http请求、便捷工具、节流防抖、对象操作、时间格式化、路由跳转、全局唯一标识符、规则校验等等。

该组件推荐配合[uv-ui组件库](https://www.uvui.cn/components/intro.html)使用，单独下载也可以在自己项目中使用，需要做相应的配置，可查看文档。强烈推荐使用[uv-ui组件库](https://www.uvui.cn/components/intro.html)，导入组件都会自动导入`uv-ui-tools`。需要在自己的项目中使用请参考[扩展配置](https://www.uvui.cn/components/setting.html)。

uv-ui破釜沉舟之兼容vue3+2、app、h5、多端小程序的uni-app生态框架，大部分组件基于uView2.x，在经过改进后全面支持vue3，部分组件做了进一步的优化，修复大量BUG，支持单独导入，方便开发者选择导入需要的组件。开箱即用，灵活配置。

# <a href="https://www.uvui.cn/js/intro.html" target="_blank">查看文档</a>

## [下载完整示例项目](https://ext.dcloud.net.cn/plugin?name=uv-ui) <small>（请不要 下载插件ZIP）</small>

### [更多插件，请关注uv-ui组件库](https://ext.dcloud.net.cn/plugin?name=uv-ui)

<a href="https://ext.dcloud.net.cn/plugin?name=uv-ui" target="_blank">

![image](https://mp-a667b617-c5f1-4a2d-9a54-683a67cff588.cdn.bspapp.com/uv-ui/banner.png)

</a>

#### 如使用过程中有任何问题反馈，或者您对uv-ui有一些好的建议，欢迎加入uv-ui官方交流群：<a href="https://www.uvui.cn/components/addQQGroup.html" target="_blank">官方QQ群</a>