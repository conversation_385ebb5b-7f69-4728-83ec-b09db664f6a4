<template>
  <view class="page">
    <base-layout
      page-title="设备详情"
      :show-back="true"
      :show-tab="false"
      @user-info="(info) => userInfo = info"
    >
      <template #content>
        <view class="content-box">
          <view class="device-info">
            <view class="wrap">
              <text class="title">
                设备名称
              </text>
              <text class="text">
                {{ deviceInfo.name }}
              </text>
            </view>
            <view class="wrap">
              <text class="title">
                设备ID
              </text>
              <text class="text">
                {{ deviceInfo.entity_id }}
              </text>
            </view>
            <view class="wrap">
              <text class="title">
                位置
              </text>
              <text class="text">
                {{ deviceInfo.addr }}
              </text>
            </view>
            <view class="wrap">
              <text class="title">
                设备状态
              </text>
              <text class="text">
                {{ deviceOpenStatus[deviceInfo.status]?.text }}
              </text>
            </view>
            <view class="wrap">
              <text class="title">
                设备情况
              </text>
              <text class="text">
                {{ deviceStatus[deviceInfo.action_type]?.text || "未知状态" }}
              </text>
            </view>
          </view>
          <view class="picker">
            <uni-datetime-picker
              v-model="selectedDates"
              :border="false"
              type="daterange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              range-separator="至"
              @change="onDateChange"
            />
          </view>
          <view class="title-sub">
            操作记录
          </view>
          <view v-if="operationLogList.length" class="device-record-list">
            <scroll-view class="list-wrap" scroll-y @scrolltolower="onOperationLogListToLower">
              <view
                v-for="(item, index) in operationLogList"
                :key="index"
                class="list-item"
              >
                <view class="wrap-1">
                  <view class="name">
                    {{ item.username }}
                  </view>
                  <view class="status" :class="deviceOpenStatus[item.is_type]?.status">
                    {{ deviceOpenStatus[item.is_type]?.text }}
                  </view>
                </view>
                <view class="wrap-2">
                  <view class="datetime">
                    {{ item.created_at }}
                  </view>
                  <view class="remark">
                    偏移 {{ item.offset }} 米
                  </view>
                </view>
                <view class="wrap-3">
                  <div v-if="item.pic" class="btn green" @click="previewPhoto(item)">
                    查看照片
                  </div>
                  <div v-else class="btn blue" @click="uploadPhoto(item)">
                    上传照片
                  </div>
                  <div class="btn green" @click="toMap(item)">
                    查看位置
                  </div>
                </view>
              </view>
              <br>
            </scroll-view>
          </view>
          <uv-empty v-else class="empty-box" name="list" icon-size="110" text-size="15" text="暂无数据" />
        </view>
      </template>
    </base-layout>
  </view>
</template>

<script setup>
import { ref, watch } from "vue";
import { onLoad, onShow, onHide, onUnload } from "@dcloudio/uni-app";
import { DeviceDetailApi, OperationLogListApi, UploadUnlockPhotoApi } from "@/utils/api.js";
import { deviceStatus, deviceOpenStatus } from "@/pages/device/data.js";

import BaseLayout from "@/components/base-layout/index.vue";
import { uniModal } from "@/utils/util";

const userInfo = ref({});
const deviceId = ref(0);
const deviceInfo = ref({});
const operationLogList = ref([]);
const currentPage = ref(1);
const pageSize = ref(50);
const total = ref(0);
const selectedDates = ref([]);
const selectedId = ref(-1);

const initDeviceDatas = async () => {
  const api = new DeviceDetailApi().token(userInfo.value.token).data({
    id: deviceId.value,
  });
  await api.send();
  const result = await api.getData();
  deviceInfo.value = result.data;
};

const initOperationLogListDatas = async () => {
  const api = new OperationLogListApi().token(userInfo.value.token).data({
    device_id: deviceId.value,
    start_time: selectedDates.value[0] || "",
    end_time: selectedDates.value[1] || "",
    page: currentPage.value,
    page_size: pageSize.value,
  });
  await api.send();
  const result = await api.getData();
  operationLogList.value = [...operationLogList.value, ...result.data.list];
  total.value = result.data.total;
};

const onOperationLogListToLower = () => {
  if (operationLogList.value.length < total.value) {
    currentPage.value += 1;
    initOperationLogListDatas();
  }
};

const onDateChange = () => {
  operationLogList.value = [];
  currentPage.value = 1;
  initOperationLogListDatas();
};

const uploadPhoto = (item) => {
  selectedId.value = item.id;
  window.android?.capture?.();
};

const previewPhoto = (item) => {
  uni.previewImage({
    urls: [`${UploadUnlockPhotoApi.baseUrl}${item.pic}`],
  });
};

const toMap = (item) => {
  uni.navigateTo({
    url: `/pages/map/index?lat=${item.lt}&lng=${item.lg}`,
  });
};

watch(() => userInfo.value, () => {
  initDeviceDatas();
  initOperationLogListDatas();
});

onLoad((options) => {
  deviceId.value = options.id;
});

onShow(() => {
  window.eventCallback = (name, v1) => {
    if (name === "capture") {
      if (selectedId.value > 0) {
        const api = new UploadUnlockPhotoApi().token(userInfo.value.token);
        api.upload(v1, async (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.code === 200) {
              api.data({
                id: selectedId.value,
                pic: data.data,
              });
              await api.send();
              const result = await api.getData();
              if (result.code === 200) {
                uniModal("照片上传成功");
                operationLogList.value = [];
                currentPage.value = 1;
                initOperationLogListDatas();
              } else {
                uniModal("照片上传失败");
              }
            }
          } catch(err) {
            uniModal("照片上传失败");
          }
        }, () => {
          uniModal("照片上传失败");
        });
      }
    }
  };
});

onHide(() => {
  window.eventCallback = () => {};
});

onUnload(() => {
  window.eventCallback = () => {};
});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>