.page {
  width: 100%;
  height: 100%;
  
  :deep(.page-content) {
    padding: 0rpx;
  }
  
  .content-box {
    flex: 1;
  
    .user-background {
      position: absolute;
      top: 0;
      width: 100%;
      height: 420rpx;
      padding: 0rpx 30rpx;
      padding-top: 200rpx;
      background: linear-gradient(to bottom, #3080F7, #b5b3fc);
  
      .user-info {
        display: flex;
        align-items: center;
  
        .avatar {
          width: 120rpx;
          height: 120rpx;
          background-size: contain;
          background-position: center center;
          background-repeat: no-repeat;
          background-image: url("../../static/default_avatar.png");
        }
  
        .username {
          color: #ffffff;
          font-size: 125%;
          font-weight: bold;
          margin-left: 30rpx;
        }
  
        .icon {
          margin-left: 12rpx;
        }
      }
  
      .option-list {
        background-color: #ffffff;
        margin-top: 60rpx;
        border-radius: 12rpx;
        box-shadow: 0rpx 0rpx 40rpx #3080F733;
  
        .item {
          position: relative;
          width: 100%;
          height: 110rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0rpx 40rpx;
          border-bottom: 2rpx solid #f6f6f6;
          
          &:last-child {
            border-bottom: none;
          }

          .value {
            color: #999999;
            font-size: 90%;
          }
        }
      }
  
      .btn-logout {
        height: 90rpx;
        background-color: #e74c3c;
        margin-top: 40rpx;
        border-radius: 12rpx;
        box-shadow: 0rpx 0rpx 40rpx #3080F733;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #ffffff;
      }
    }
  }
}