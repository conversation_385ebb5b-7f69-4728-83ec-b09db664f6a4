<template>
  <view v-if="showPage" class="page">
    <base-layout page-title="操作" :selected-index="2" @user-info="(info) => userInfo = info">
      <template #content>
        <view class="content-box">
          <view class="spinner">
            <l-circle v-model:current="currentPercent" class="progress" :size="ringWidth" :duration="600" :trail-width="trailWidth" trail-color="#A4C5D5" :stroke-width="strokeWidth" :stroke-color="strokeColors" :percent="spinnerPercent" />
            <view class="circle">
              <view class="percent">
                {{ Number(currentPercent).toFixed(0) }}%
              </view>
            </view>
          </view>
          <view class="device-infos">
            <text v-if="deviceName" class="device-name">
              设备名称：{{ deviceName }}
            </text>
            <text v-if="deviceId" class="device-id">
              设备ID：{{ deviceId }}
            </text>
          </view>
          <view class="btn-group">
            <view class="btn btn-unlock" :selected="operation === 'unlock'" @click="setOperation('unlock')">
              <text class="text">
                开锁
              </text>
            </view>
            <view class="btn btn-lock" :selected="operation === 'lock'" @click="setOperation('lock')">
              <text class="text">
                关锁
              </text>
            </view>
          </view>
        </view>
      </template>
    </base-layout>
  </view>
</template>

<script setup>
import { computed, ref, watch } from "vue";
import { onLoad, onShow, onHide, onUnload } from "@dcloudio/uni-app";
import { uniModal } from "@/utils/util.js";
import { deviceOpenStatus } from "@/pages/device/data.js";
import { DeviceDetailByEntityIdApi, LocalDeviceListApi, OperationLogCreateApi } from "@/utils/api.js";
import Config from "@/config.json";

import BaseLayout from "@/components/base-layout/index.vue";
import { getVarientConfig } from "@/varient";

const showPage = ref(false);
const userInfo = ref({});
const availableDevices = ref([]);
const locationInfo = ref({});
const operation = ref("");
const step = ref(0);
const deviceId = ref("");
const deviceName = ref("");
const currentPercent = ref(0);
const spinnerPercent = ref(0);
const trailWidth = uni.upx2px(60);
const strokeWidth = uni.upx2px(60);
const ringWidth = uni.upx2px(355 + 60 * 2).toFixed(0);

const strokeColors = computed(() => {
  if (currentPercent.value < 15) {
    return ["#5399D5", "#5399D5"];
  } else {
    return ["#5399D5", "#72D8D7", "#7EF0D8", "#549CD5", "#5399D5"];
  }
});

const setOperation = (value) => {
  const lastLocation = window.android?.getLastLocation?.();
  // if (lastLocation) {
  const [latitude, longitude] = lastLocation?.split?.(",") ?? [];
  locationInfo.value = {
    latitude,
    longitude,
  };
  if (operation.value === value) {
    operation.value = "";
  } else {
    operation.value = value;
  }
  window.android?.setMode?.("normal");
  window.android?.setAction?.(Config.operationName[operation.value]);
  // } else {
  // uniModal("定位数据获取失败，请检查定位功能是否开启");
  // }
};

const setState = (_step, _percent) => {
  step.value = _step;
  spinnerPercent.value = _percent;
};

const onDeviceNotFound = async (deviceId) => {
  const api = new DeviceDetailByEntityIdApi().token(userInfo.value.token).data({
    entity_id: deviceId,
  });
  await api.send();
  const result = await api.getData();
  if (result.data?.id) {
    uniModal("该设备未授权，是否向管理员申请设备权限？", true, (res) => {
      if (res.confirm) {
        uni.navigateTo({
          url: `/pages/device-require/index?id=${result.data.id}`,
        });
      }
    });
  } else {
    uniModal("该设备不存在");
  }
};

const onWrongDeviceInfo = () => {
  uniModal("设备信息错误");
};

const onLockOrUnlockSuccess = async (operation) => {
  const api = new OperationLogCreateApi().token(userInfo.value.token).data({
    user_id: userInfo.value.id,
    username: userInfo.value.username,
    phone: userInfo.value.phone,
    device_id: availableDevices.value.find((device) => device.entity_id === deviceId.value)?.id,
    is_type: Object.entries(deviceOpenStatus).find(([, value]) => value.name === operation)?.[0],
    lg: locationInfo.value.longitude || "",
    lt: locationInfo.value.latitude || "",
  });
  await api.send();
  const result = await api.getData();
  if (result.code !== 200) {
    uniModal("操作记录上传失败");
  }
};

const setCallbacks = () => {
  window.actionCallback = (name, v1, v2) => {
    if (name === "start") {
      setState(0, 0);
    } else if (name === "detect") {
      if (v1) {
        deviceName.value = availableDevices.value.find((device) => device.entity_id === v1)?.name || "";
        if (deviceName.value) {
          deviceId.value = v1;
        }
        setState(0, 0);
        window.android?.setMode?.("normal");
        window.android?.setAction?.(Config.operationName[operation.value]);
      } else {
        deviceId.value = "";
        deviceName.value = "";
        if (operation.value && step.value < 3) {
          // connection lost
          // setState(3, 0);
        }
      }
    } else if (name === "info" && v1 === "not found") {
      onDeviceNotFound(v2);
      setState(3, 0);
    } else if (name === "info" && v1 === "wrong") {
      onWrongDeviceInfo();
      setState(3, 0);
    } else if (name === "on") {
      if (v1 === "charge") {
        setState(1, 60);
      } else if (v1 === Config.operationName.lock) {
        setState(2, 90);
        onLockOrUnlockSuccess("lock");
      } else if (v1 === Config.operationName.unlock) {
        setState(2, 90);
        onLockOrUnlockSuccess("unlock");
      }
    } else if (name === Config.operationName.lock) {
      if (v1 === "true") {
        setState(3, 100);
        setOperation("");
      } else if (v1 === "false") {
        // setState(3, 0);
      }
    } else if (name === Config.operationName.unlock) {
      if (v1 === "true") {
        setState(3, 100);
        setOperation("");
      } else if (v1 === "false") {
        // setState(3, 0);
      }
    }
  };
};

const clearCallbacks = () => {
  window.actionCallback = () => {};
};

const initDatas = async () => {
  const api = new LocalDeviceListApi();
  await api.sync(userInfo.value.token, 0);
  const datas = api.load();
  availableDevices.value = datas;
};

const setLockInfos = () => {
  const lockInfos = availableDevices.value.map((device) => ({
    id: device.entity_id,
    key: device.password || "",
  }));
  window.android?.setLockInfos?.(JSON.stringify(lockInfos));
};

watch(() => userInfo.value, async () => {
  await initDatas();
  setLockInfos();
});

onLoad(() => {
  const varientConfig = getVarientConfig();
  if (varientConfig.captureIndex) {
    uni.redirectTo({
      url: "/pages/index/index_capture",
    });
  } else if (varientConfig.bioIndex) {
    uni.redirectTo({
      url: "/pages/index/index_bio",
    });
  } else {
    showPage.value = true;
  }
});

onShow(() => {
  setCallbacks();
});

onHide(() => {
  clearCallbacks();
  window.android?.setMode?.("");
  window.android?.setAction?.("");
});

onUnload(() => {
  clearCallbacks();
  window.android?.setMode?.("");
  window.android?.setAction?.("");
});
</script>

<style lang="less" scoped>
  @import "./index.less";

  :deep(.l-liquid--outline) {
    border-color: #3080F7;
  }
</style>