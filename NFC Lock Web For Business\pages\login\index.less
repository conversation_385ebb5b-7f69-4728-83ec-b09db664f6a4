.page {
  width: 100%;
  height: 100%;
  padding: 50rpx;
  
  .top {
    margin-top: 160rpx;
    
    .title {
      font-size: 45rpx;
      font-weight: bold;
    }
    
    .title-sub {
      margin-top: 15rpx;
      color: #555;
      
      .register {
        margin-left: 14rpx;
        color: #3080F7;
      }
    }
  }
  
  .form {
    margin-top: 60rpx;
    display: flex;
    flex-direction: column;
    
    .form-item {
      margin-bottom: 20rpx;
      display: flex;
      align-items: center;
      
      .verify-code {
        color: #3080F7;
        font-size: 24rpx;
        
        &[disabled="true"] {
          color: #999;
        }
      }
    }
  }
  
  .btns {
    margin-top: 40rpx;
    
    :nth-child(n) {
      margin-top: 20rpx;
      padding: 20rpx 0rpx;
      color: white;
      border-radius: 10rpx;
      display: flex;
      justify-content: center;
    }
    
    .first {
      background-color: #3080F7;
      box-shadow: 0rpx 0rpx 10rpx #3080F722;
    }
    
    .second {
      color: #3080F7;
      background-color: transparent;
    }
  }
}