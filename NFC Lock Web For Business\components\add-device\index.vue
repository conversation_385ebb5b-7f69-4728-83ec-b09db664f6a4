<template>
  <my-drawer
    :title="type === 'form' ? '编辑设备' : '添加设备'"
    :height="950"
    :open="show"
    @close="onClose"
  >
    <template #content>
      <view
        v-if="type === 'add'"
        class="nfc-box"
      >
        <image
          class="nfc"
          src="../../static/nfc.png"
          mode="aspectFit"
        />
        <view class="title">
          NFC
        </view>
      </view>
      <view
        v-if="type === 'form'"
        class="form"
      >
        <view class="form-box">
          <view class="form-item">
            <uv-input
              v-model="formModel.deviceName"
              placeholder="请输入设备名称"
              border="none"
              maxlength="20"
              :custom-style="{ backgroundColor: '#f6f6f6', padding: '24rpx' }"
            />
          </view>
          <view class="form-item">
            <uv-input
              v-model="formModel.address"
              placeholder="请输入设备地址"
              border="none"
              maxlength="40"
              :custom-style="{ backgroundColor: '#f6f6f6', padding: '24rpx' }"
            />
          </view>
        </view>
        <view class="btns">
          <view @click="onSubmit">
            确定
          </view>
        </view>
      </view>
    </template>
  </my-drawer>
</template>

<script setup>
import { ref, watch } from "vue";

import MyDrawer from "../drawer/index.vue";

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "",
  },
  form: {
    type: Object,
    default: () => ({}),
  },
});

const emits = defineEmits(["submit", "update:form", "close"]);

const formModel = ref({});

const onSubmit = (value) => {
  if (formModel.value.deviceName) {
    emits("update:form", formModel.value);
    emits("submit");
  } else {
    uni.showModal({
      title: "提示",
      content: "请输入设备名称",
      confirmText: "确定",
      showCancel: false,
    });
  }
};

const onClose = () => {
  emits("close");
};

watch(() => props.show, () => {
  formModel.value = props.form;
});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>