<template>
  <my-drawer
    :title="i18n('componentNameSelectorDevice')"
    :height="-1"
    :open="show"
    @close="onBack"
  >
    <template #content>
      <view class="selector">
        <uv-tabs
          :list="navSelection"
          :scrollable="false"
          line-color="#2977FF"
          :active-style="{ color: '#2977FF', fontWeight: 'bold' }"
          :item-style="{ height: '80rpx' }"
          @click="onNavClick"
        />
        <view class="list-box">
          <my-list
            ref="mylist"
            v-model:has-selection="hasSelection"
            :datas="listDatas"
            :alert="i18n(selectedNav === 0 ? 'modalContentConfirmAuth' : 'modalContentConfirmCancelAuth')"
            :page-size="pageSize"
            :total="pageTotal"
            :has-pagination="false"
            :empty="i18n('defaultEmpty')"
            @select="onSelect"
            @page-change="onPageChange" @scrolltolower="onScrollToLower"
          />
        </view>
      </view>
      <view
        class="select-all"
        @click="onSelectAll"
      >
        {{ mylist?.isAllSelected() ? i18n('btnCancelSelection') : i18n('btnSelectAll') }}
      </view>
    </template>
  </my-drawer>
</template>

<script setup>
import { nextTick, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { NotShareApi, ShareApi, SharedDeviceApi } from "@/utils/api.js";

import MyDrawer from "../drawer/index.vue";
import MyList from "../list/index.vue";
import Empty from "@/components/empty/index.vue";

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  sid: {
    type: Number,
    default: -1,
  },
  info: {
    type: Object,
    default: () => ({}),
  },
});

const emits = defineEmits(["update:show"]);

const { tm: i18n } = useI18n();
const mylist = ref(null);
const navSelection = ref([i18n("btnDeviceAuth"), i18n("btnCancelAuth")].map((name) => ({ name })));
const selectedNav = ref(0);
const hasSelection = ref(false);
const currentPage = ref(1);
const pageSize = 50;
const pageTotal = ref(0);
const listDatas = ref([]);

const onInit = async () => {
  if (props.info) {
    const api = new SharedDeviceApi().token(props.info.token).data({
      user_id: props.sid,
      is_share: 1 - selectedNav.value,
      page: currentPage.value,
      size: pageSize,
    });
    await api.send();
    const result = await api.getData();
    listDatas.value = [
      ...listDatas.value,
      ...result.data.list.map((item) => ({
        id: item.id,
        title: item.name,
        titleSub: item.remark,
      })),
    ];
    pageTotal.value = result.data.total;
  }
};

const onPageChange = (page) => {
  currentPage.value = page;
  onInit();
};

const onScrollToLower = () => {
  if (currentPage.value < Math.ceil(pageTotal.value / pageSize)) {
    onPageChange(currentPage.value + 1);
  }
};

const onNavClick = ({ index }) => {
  hasSelection.value = false;
  nextTick(() => {
    hasSelection.value = true;
    selectedNav.value = index;
    currentPage.value = 1;
    listDatas.value = [];
    onInit();
  });
};

const onSelectAll = () => {
  if (mylist.value.isAllSelected()) {
    mylist.value.selectReverse();
  } else {
    mylist.value.selectAll();
  }
};

const onSelect = async (selection) => {
  let Api = null;
  let hint = "";
  if (selectedNav.value === 0) {
    Api = ShareApi;
  } else if (selectedNav.value === 1) {
    Api = NotShareApi;
  }
  if (selection && Api) {
    const requests = selection.map((item) => new Api().token(props.info.token).data({
      device_id: Number(item),
      user_id: props.sid,
    }));
    await Promise.all(requests.map((item) => item.send()));
    const results = await Promise.all(requests.map((item) => item.getData()));
    if (results.every((item) => item.code === 0)) {
      if (selectedNav.value === 0) {
        hint = i18n("modalContentAuthSucceeded");
      } else if (selectedNav.value === 1) {
        hint = i18n("modalContentCancelAuthSucceeded");
      }
    } else {
      if (selectedNav.value === 0) {
        hint = i18n("modalContentAuthFailed");
      } else if (selectedNav.value === 1) {
        hint = i18n("modalContentCancelAuthFailed");
      }
    }
    uni.showModal({
      title: i18n("modalTitleAlert"),
      content: hint,
      confirmText: i18n("modalBtnConfirm"),
      showCancel: false,
    });
  }
  emits("update:show", false);
};

const onBack = () => {
  hasSelection.value = false;
  emits("update:show", false);
};

watch(() => props.show, () => {
  if (props.show) {
    hasSelection.value = true;
    currentPage.value = 1;
    selectedNav.value = 0;
    pageTotal.value = 0;
    listDatas.value = [];
    onInit();
  }
});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>