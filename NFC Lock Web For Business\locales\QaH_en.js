export const QaH_en = [
  {
    id: 1,
    title: "The lock opens but won't close",
    content: `<view>You need to hold your phone close to the lock and try unlocking it again, then attempt to press the lock beam back. If it still doesn't work, you may need to try a few more times.</view>
    <view style="color: red">Friendly reminder: Keep the phone in place for 2-3 seconds after unlocking or wait until the app indicates that unlocking was successful before removing the phone, ensuring that the phone provides sufficient power to the lock to avoid insufficient power preventing the lock beam from closing.</view>`,
  },
  {
    id: 2,
    title: "The phone is close to the lock but no sound is heard",
    content: `<ol style="padding: 0px 24px !important"><li>First, confirm whether the NFC function on your phone is turned on;</li>
    <li>Observe and identify where the NFC module is located on your phone;</li>
    <li>Bring the phone's NFC module as close to the lock's antenna as possible, ideally touching the lock, and slowly move it up, down, left, and right. If you hear a "beep," it indicates that the lock has paired with the phone's NFC. Then, try to maintain this position so the phone's NFC can charge the lock for unlocking;</li>
    </ol>`,
  },
  {
    id: 3,
    title: "The app indicates successful unlocking, but the lock did not pop open",
    content: "<view>Please try unlocking again, ensuring that the phone's NFC module remains paired with the lock, and do not move the phone during the unlocking process to ensure all unlocking actions are completed.</view>",
  },
  {
    id: 4,
    title: "How to check if your phone supports NFC",
    content: `<strong>Android Phones</strong>
    
    <view>You can confirm whether your phone has NFC functionality through the system settings. The specific steps are as follows:</view>
    <ol style="padding: 0px 24px !important">
      <li>Open Settings and click on Other Networks and Connections;</li>
      <li>Check if there is an NFC settings option. If you do not see the NFC settings option, it means your device does not support NFC functionality.</li>
    </ol>
    <strong>Apple Phones</strong>
    <ol style="padding: 0px 24px !important">
      <li>Open your Apple phone and find the "Settings" button in the operating system, then click to enter;</li>
      <li>In "Settings," find the "General" option and click to enter;</li>
      <li>In "General," select the "NFC" option;</li>
      <li>After entering the "NFC" configuration interface, click the button to the right of "NFC" to turn it on or off;</li>
      <li>Click to turn it on; the button should light up green.</li>
    </ol>`,
  },
  {
    id: 5,
    title: "Common phone NFC antenna locations",
    content: `
    <details>
      <summary style="color: #409eff; text-decoration: underline">Xiaomi Phone NFC Antenna Location</summary>
      <img style="width: 100%; height: auto; background-size: cover;" src="https://lock.lcxxjs.cn/20240913/d44xszrh7ku1uyuscv.jpg"></img>
    </details>
    
    <details>
      <summary style="color: #409eff; text-decoration: underline">Honor Phone NFC Antenna Location</summary>
      <img style="width: 100%; height: auto; background-size: cover;" src="https://lock.lcxxjs.cn/20240913/d44xt8r3nxckb7dz68.jpg"></img>
    </details>
    
    <details>
      <summary style="color: #409eff; text-decoration: underline">OPPO Phone NFC Antenna Location</summary>
      <img style="width: 100%; height: auto; background-size: cover;" src="https://lock.lcxxjs.cn/20240913/d44tnbm2uctm8vjrba.jpg"></img>
    </details>
    
    <br/>
    <br/>
    <br/>`,
  },
];
