<script>
import { getVarientConfig } from "./varient.js";

export default {
  onShow() {
    const varientConfig = getVarientConfig();
    if (varientConfig.reLoginWhenEnter) {
      uni.reLaunch({
        url: "/pages/login/index",
      });
    }
  },
};
</script>

<style lang="less">
  * {
    box-sizing: border-box;
  }

  page {
    width: 100%;
    height: 100%;
    color: #333;
    background-color: white;
    font-family: "PingFang SC";
    font-size: 28rpx;
  }

  uni-modal .uni-modal__bd {
    word-break: break-word;
  }
</style>