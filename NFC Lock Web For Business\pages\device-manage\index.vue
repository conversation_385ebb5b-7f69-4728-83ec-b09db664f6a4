<template>
  <view class="page">
    <base-layout
      page-title="设备管理"
      :show-back="true"
      :show-tab="false"
      @user-info="(info) => userInfo = info"
    >
      <template #content>
        <view class="content-box">
          <view
            class="device-info"
            :type="addStatus"
          >
            <view class="wrap">
              <text class="title">
                设备名称
              </text>
              <text class="text">
                <view v-if="addType === 'single'">
                  {{ deviceInfoModel.deviceName || "--" }}
                </view>
                <view v-else-if="addType === 'batch'">
                  {{ deviceList.find(item => item.entity_id === actionDeviceId)?.name || "--" }}
                </view>
                <view v-else>
                  --
                </view>
              </text>
            </view>
            <view class="wrap">
              <text class="title">
                设备ID
              </text>
              <text class="text">
                {{ actionDeviceId || "--" }}
              </text>
            </view>
            <view
              v-if="addStatus"
              class="wrap"
              style="margin-top: 40rpx;"
            >
              <text v-if="addStatus === 'normal'">
                搜索中...
              </text>
              <text v-if="addStatus === 'success'">
                添加成功
              </text>
              <text v-if="addStatus === 'error'">
                添加失败
              </text>
            </view>
          </view>
          <view class="btn-group">
            <view
              class="btn btn-single"
              :selected="addType === 'single'"
              @click="startAdd('single')"
            >
              单个添加
            </view>
            <view
              class="btn btn-batch"
              :selected="addType === 'batch'"
              @click="startAdd('batch')"
            >
              批量配置
            </view>
          </view>
          <view class="wrap-middle">
            <view class="text">
              设备列表
            </view>
            <picker
              :value="filterStatus"
              :range="filterStatusList"
              @change="setFilterStatus"
            >
              <view class="wrap-filter">
                <text class="filter">
                  {{ filterStatusList[filterStatus] }}
                </text>
                <uv-icon
                  name="arrow-down"
                  size="16"
                  color="#888888"
                />
              </view>
            </picker>
          </view>
          <scroll-view
            v-if="(filteredDeviceList || deviceList).length"
            class="device-list"
            scroll-y
            :scroll-top="deviceListScrollTop"
          >
            <view
              v-for="(item, index) in (filteredDeviceList || deviceList)"
              :key="index"
              class="list-item"
              :selected="index === highlightIndex"
            >
              <view class="infos">
                <view class="wrap-1">
                  <view class="name"> 
                    <text>{{ item.name }}</text>
                    <uv-icon class="icon" name="edit-pen" size="24" color="#666666" @click="editDeviceName(item)" />
                  </view>
                  <view class="status" :type="deviceStatus[item.action_type]?.status || 'unknown'">
                    {{ deviceStatus[item.action_type]?.text || "未知状态" }}
                  </view>
                </view>
                <view class="device-id">
                  {{ item.entity_id }}
                </view>
              </view>
            </view>
          </scroll-view>
          <uv-empty v-else class="empty-box" name="list" icon-size="110" text-size="15" text="暂无数据" />
        </view>

        <add-device
          v-model:form="deviceInfoModel"
          :show="showDeviceInfoModel"
          type="form"
          @submit="onDeviceInfoSubmit"
          @close="onDeviceInfoClose"
        />
      </template>
    </base-layout>
    <my-edit-user-info v-model:form="deviceNameForm" :show="showEditDeviceName" type="deviceName" @submit="onDeviceNameSubmit" @close="showEditDeviceName = false" />
  </view>
</template>

<script setup>
import { ref, watch } from "vue";
import { onLoad, onShow, onHide, onUnload } from "@dcloudio/uni-app";
import { y } from "@/utils/lcfruit.js";
import { data } from "@/utils/data1.js";
import { uniModal } from "@/utils/util.js";
import { DeviceCreateApi, DeviceListApi, DeviceUpdateApi } from "@/utils/api.js";
import { deviceStatus } from "@/pages/device/data.js";

import BaseLayout from "@/components/base-layout/index.vue";
import AddDevice from "@/components/add-device/index.vue";
import MyEditUserInfo from "@/components/edit-user-info/index.vue";

const userInfo = ref({});
const actionDeviceId = ref("");
const selectedEditeviceNameId = ref(-1);
const showEditDeviceName = ref(false);
const showDeviceInfoModel = ref(false);
const deviceNameForm = ref({});
const deviceInfoModel = ref({});
const deviceList = ref([]);
const highlightIndex = ref(-1);
const deviceListScrollTop = ref(0);
const filterStatus = ref(0);
const filterStatusList = ["全部设备", ...Object.values(deviceStatus).map(item => item.text)];
const filteredDeviceList = ref(null);
const addType = ref("");
const addStatus = ref("");
const currentPage = ref(1);
const pageSize = ref(50);
const total = ref(0);

const editDeviceName = (item) => {
  deviceNameForm.value = {};
  deviceNameForm.value.deviceName = item.name;
  selectedEditeviceNameId.value = item.id;
  showEditDeviceName.value = true;
};

const onDeviceNameSubmit = async () => {
  const api = new DeviceUpdateApi().token(userInfo.value.token).data({
    id: selectedEditeviceNameId.value,
    name: deviceNameForm.value.deviceName,
  });
  await api.send("确定要修改吗？");
  const result = await api.getData();
  if (result.code === 200) {
    showEditDeviceName.value = false;
    uniModal("修改成功");
    await getAllDevices();
  } else {
    uniModal("修改失败");
  }
};

const initDatas = async () => {
  const api = new DeviceListApi().token(userInfo.value.token).data({
    page: currentPage.value,
    page_size: pageSize.value,
  });
  await api.send();
  const result = await api.getData();
  deviceList.value = [...deviceList.value, ...result.data.list];
  total.value = result.data.total;
};

const getAllDevices = async () => {
  deviceList.value = [];
  currentPage.value = 1;
  // 刷新数据时清除 highlightIndex
  highlightIndex.value = -1;
  for (let i = 1; i <= 100; i++) {
    await initDatas();
    currentPage.value += 1;
    if (deviceList.value.length >= total.value) {
      break;
    }
  }
};
  
const setFilterStatus = (e) => {
  filterStatus.value = e.detail.value;
  // 筛选数据时清除 highlightIndex
  highlightIndex.value = -1;
  if (filterStatus.value === 0) {
    filteredDeviceList.value = null;
  } else {
    filteredDeviceList.value = deviceList.value.filter(item => item.action_type === filterStatus.value);
  }
};

const scrollToHighlightIndex = async (id) => {
  if (id) {
    const currentDeviceList = filteredDeviceList.value || deviceList.value;
    highlightIndex.value = currentDeviceList.findIndex(item => item.entity_id === id);
  }
  const query = uni.createSelectorQuery();
  query.selectAll(".list-item").boundingClientRect(rects => {
    if (rects && rects.length > 0) {
      const scrollTop = rects.slice(0, highlightIndex.value).reduce((sum, rect) => sum + rect.height, 0);
      deviceListScrollTop.value = scrollTop;
    }
  }).exec();
};

const singleAdd = () => {
  const targetLockInfo = deviceList.value.map((item) => ({ id: item.entity_id}));
  window.android?.setTargetLockInfos?.(JSON.stringify(targetLockInfo));
  window.android?.setMode?.("excludes");
  window.android?.setAction?.("setKey");
};

const batchAdd = () => {
  const targetLockInfo = deviceList.value.map((item) => ({ id: item.entity_id}));
  window.android?.setTargetLockInfos?.(JSON.stringify(targetLockInfo));
  window.android?.setMode?.("includes");
  window.android?.setAction?.("setKey");
};

const finishAdd = () => {
  window.android?.setTargetLockInfos?.(JSON.stringify([]));
  window.android?.setMode?.("");
  window.android?.setAction?.("");
};

const startAdd = (type) => {
  if (type === addType.value) {
    // 取消操作，设置 addType 和 addStatus 为空
    addType.value = "";
    addStatus.value = "";
    finishAdd();
  } else {
    addType.value = type;
    if (type === "single") {
      // 单个添加，显示设备信息表单
      showDeviceInfoModel.value = true;
    } else if (type === "batch") {
      // 批量添加直接设置 setKey 动作和 includes 模式，并设置目标设备为设备列表
      addStatus.value = "normal";
      batchAdd();
    }
  }
};

const onDeviceInfoSubmit = () => {
  showDeviceInfoModel.value = false;
  // 单个添加表单填写完成，设置 action 为 setKey，模式为 excludes，并设置目标设备为设备列表
  addStatus.value = "normal";
  singleAdd();
};

const onDeviceInfoClose = () => {
  showDeviceInfoModel.value = false;
  addType.value = "";
  addStatus.value = "";
};

const onSetKeySuccess = async (entityId, key, deviceInfo) => {
  const device = deviceList.value.find(item => item.entity_id === entityId);
  if (device) {
    const api = new DeviceUpdateApi().token(userInfo.value.token).data({
      id: device.id,
      password: key,
      action_type: 3,
    });
    await api.send();
    const result = await api.getData();
    if (result.code === 200) {
      addStatus.value = "success";
      await getAllDevices();
      scrollToHighlightIndex(entityId);
    } else {
      addStatus.value = "error";
    }
  } else {
    const api = new DeviceCreateApi().token(userInfo.value.token).data({
      entity_id: entityId,
      name: deviceInfo.deviceName,
      addr: deviceInfo.address || "",
      password: key,
      action_type: 3,
    });
    await api.send();
    const result = await api.getData();
    if (result.code === 200) {
      addStatus.value = "success";
      await getAllDevices();
      scrollToHighlightIndex(entityId);
    } else {
      addStatus.value = "error";
    }
  }
};

const onDeviceConfigured = async (entityId) => {
  const device = deviceList.value.find(item => item.entity_id === entityId);
  if (device) {
    if (device.action_type === 1) {
      const api = new DeviceUpdateApi().token(userInfo.value.token).data({
        id: device.id,
        action_type: 2,
      });
      await api.send();
      await api.getData();
      await getAllDevices();
      uniModal("设备配置错误");
    } else if (device.action_type === 2) {
      uniModal("设备配置错误");
    } else {
      uniModal("设备已配置");
    }
    scrollToHighlightIndex(entityId);
  } else {
    uniModal("设备已配置");
  }
};

const setCallback = () => {
  // 设置 actionCallback 用于 android webview 回调
  // name = detect 且检测到设备时，v1 为设备 Id
  // name = setKey 且设置成功时，v1 为设备密码
  window.actionCallback = (name, v1, v2) => {
    if (name === "start") {
      actionDeviceId.value = "";
      if (addType.value === "batch") {
        addStatus.value = "normal";
        batchAdd();
      } else {
        addStatus.value = "";
      }
    } else if (name === "detect") {
      if (v1) {
        actionDeviceId.value = v1;
      } else {
        if (addStatus.value === "normal") {
          addStatus.value = "error";
        }
        if (addType.value === "single") {
          addType.value = "";
        }
      }
    } else if (name === "setKey") {
      if (v1) {
        onSetKeySuccess(actionDeviceId.value, v1, { ...deviceInfoModel.value });
        deviceInfoModel.value = {};
      } else if (v2 === "not new") {
        onDeviceConfigured(actionDeviceId.value);
      } else if (v2 === "false") {
        addStatus.value = "error";
      }
    } else if (name === "target" && v1 === "wrong") {
      if (addType.value === "single") {
        uniModal("设备列表中已存在该设备");
      } else if (addType.value === "batch") {
        uniModal("设备列表中不存在该设备");
      }
    }
  };
};

const clearCallback = () => {
  window.actionCallback = () => {};
};

const setLockInfos = () => {
  const d = y(data);
  const s = eval(d);
  const temporalLockInfos = [{
    id: 0,
    supervisorKey: s,
  }];
  const lockInfos = [{
    id: 0,
  }];
  window.android?.setTemporalLockInfos?.(JSON.stringify(temporalLockInfos));
  window.android?.setLockInfos?.(JSON.stringify(lockInfos));
};

watch(() => userInfo.value, async () => {
  await getAllDevices();
  setLockInfos();
});

onLoad(() => {

});

onShow(() => {
  setCallback();
});

onHide(() => {
  clearCallback();
  finishAdd();
});

onUnload(() => {
  clearCallback();
  finishAdd();
});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>