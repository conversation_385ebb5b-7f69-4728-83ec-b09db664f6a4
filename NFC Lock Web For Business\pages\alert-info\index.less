.page {
  width: 100%;
  height: 100%;

  :deep(.page-content) {
    padding: 0rpx;
  }

  .content-box {
    height: 100%;
    display: flex;
    flex-direction: column;

    .picker {
      padding: 6rpx 30rpx;
      margin-bottom: 20rpx;
    }

    .empty-box {
      height: 0rpx;
      flex: 1;
    }

    .list-box {
      flex: 1;
      height: 0;

      .device-info {
        color: #333;
        background-color: #3080F711;
        border: 2rpx solid#3080F7;
        box-shadow: 0rpx 0rpx 40rpx #3080F722;
        border-radius: 12rpx;
        padding: 12rpx 20rpx;
        margin: 0rpx 32rpx;
        margin-top: 20rpx;

        .main-title {
          font-weight: bold;
          color: #3080F7;
        }

        .wrap {
          color: #333;
          font-size: 85%;
          display: flex;
          align-items: center;

          .title {
            font-weight: bold;
            width: 110rpx;
            text-align-last: justify;
          }

          .text {
            flex: 1;
            margin-left: 20rpx;

            &.highlight {
              color: #3080F7;
              font-weight: bold;
            }
          }
        }

        &[type="normal"] {
          background-color: #3080F711;
          border-color: #3080F7;
          box-shadow: 0rpx 0rpx 40rpx #3080F722;

          .main-title {
            color: #3080F7;
          }

          .text {
            &.highlight {
              color: #3080F7;
              font-weight: bold;
            }
          }
        }

        &[type="success"] {
          background-color: #8bc34a11;
          border-color: #8bc34a;
          box-shadow: 0rpx 0rpx 40rpx #8bc34a22;

          .main-title {
            color: #8bc34a;
          }

          .text {
            &.highlight {
              color: #8bc34a;
              font-weight: bold;
            }
          }
        }

        &[type="warning"] {
          background-color: #ff980011;
          border-color: #ff9800;
          box-shadow: 0rpx 0rpx 40rpx #ff980022;

          .main-title {
            color: #ff9800;
          }

          .text {
            &.highlight {
              color: #ff9800;
              font-weight: bold;
            }
          }
        }

        &[type="error"] {
          background-color: #e74c3c11;
          border-color: #e74c3c;
          box-shadow: 0rpx 0rpx 40rpx #e74c3c22;

          .main-title {
            color: #e74c3c;
          }

          .text {
            &.highlight {
              color: #e74c3c;
              font-weight: bold;
            }
          }
        }
      }
    }
  }
}
