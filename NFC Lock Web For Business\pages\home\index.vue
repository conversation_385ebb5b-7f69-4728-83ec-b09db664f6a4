<template>
  <view class="page">
    <base-layout
      page-title="首页"
      :selected-index="0"
      @user-info="(info) => userInfo = info"
    >
      <template #content>
        <view class="content-box">
          <view class="grid-item-primary device-number">
            <view class="wrap-1">
              <view class="icon" />
              <view class="text">
                设备数量
              </view>
            </view>
            <view class="number">
              {{ statistic.device_num }}
            </view>
          </view>
          <view class="grid-item-primary unlock-count">
            <view class="wrap-1">
              <view class="icon" />
              <view class="text">
                开锁次数
              </view>
            </view>
            <view class="number">
              {{ statistic.lock_open_num }}
            </view>
          </view>
          <view
            id="video"
            class="video"
          >
            <uv-empty
              v-if="!videoLoaded"
              text="视频加载中..."
            />
            <video
              id="videoElement"
              class="video-content"
              :style="{ height: `${videoHeight}px` }"
              :src="statistic.video_url" 
              :autoplay="true" 
              :loop="true"
              object-fit="contain"
              @loadedmetadata="onVideoLoadedMetadata"
            />
          </view>
          <view class="grid-item instruction">
            <view class="icon" />
            <view class="text">
              操作说明
            </view>
          </view>
          <view
            v-if="userInfo.role_id === 1"
            class="grid-item device-manage"
            @click="toPage('device-manage')"
          >
            <view class="icon" />
            <view class="text">
              设备管理
            </view>
          </view>
          <view
            class="grid-item operation-log"
            @click="toPage('operation-log')"
          >
            <view class="icon" />
            <view class="text">
              操作日志
            </view>
          </view>
          <view
            v-if="userInfo.role_id === 1"
            class="grid-item alert-info"
            @click="toPage('alert-info')"
          >
            <view class="icon" />
            <view class="text">
              告警信息
            </view>
          </view>
        </view>
      </template>
    </base-layout>
  </view>
</template>

<script setup>
import { ref, watch } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { HomeStatisticApi } from "@/utils/api.js";

import BaseLayout from "@/components/base-layout/index.vue";

const userInfo = ref({});
const statistic = ref({});
const videoHeight = ref(0);
const videoLoaded = ref(false);

const initDatas = async () => {
  const api = new HomeStatisticApi().token(userInfo.value.token).data({});
  await api.send();
  const result = await api.getData();
  statistic.value = result.data;
};

const onVideoLoadedMetadata = (e) => {
  const width = e.detail.width; 
  const height = e.detail.height;
  const proportion = width / height;
  const query = uni.createSelectorQuery();
  query.select("#video").boundingClientRect(data => {
    videoHeight.value = data.width / proportion;
  }).exec();
  videoLoaded.value = true;
};

const toPage = (path) => {
  uni.navigateTo({
    url: `/pages/${path}/index`,
  });
};

watch(() => userInfo.value, () => {
  initDatas();
});

onLoad(() => {

});

onShow(() => {
  uni.createVideoContext("videoElement").play();
});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>