<template>
  <view class="page">
    <view class="background" />
    <base-layout :page-title="i18n('pageAboutUs')" color="#eee" :show-back="true" :show-tab="false" @user-info="(info) => userInfo = info" @version-info="(info) => versionInfo = info">
      <template #content>
        <view class="app-info">
          <image src="../../static/ic_logo.png" mode="aspectFit" />
          <view class="version">
            {{ i18n("versionCode") }}: {{ versionInfo.version }}
          </view>
        </view>
        <view class="buttons">
          <view @click="onUpdate">
            <uv-badge v-if="versionInfo.url" :absolute="true" :offset="[15, 10]" :is-dot="true" type="error" />
            <view>{{ i18n("btnUpdate") }}</view>
            <view v-if="versionInfo.url" class="hint-text">
              {{ i18n("newVersionHint") }}
            </view>
          </view>
          <view @click="toProtocol">
            <view>{{ i18n("btnUserProtocol") }}</view>
            <uv-icon name="arrow-right" />
          </view>
          <view @click="toPrivacy">
            <view>{{ i18n("btnPrivacy") }}</view>
            <uv-icon name="arrow-right" />
          </view>
          <view @click="openEmail">
            <view>{{ i18n("btnContactUs") }}</view>
            <view class="mail">
              {{ emailAddress }}
            </view>
          </view>
        </view>
      </template>
    </base-layout>
  </view>
</template>

<script setup>
import { ref } from "vue";
import { useI18n } from "vue-i18n";

import BaseLayout from "@/components/base-layout/index.vue";

const { tm: i18n } = useI18n();
const emailAddress = ref("<EMAIL>");
const userInfo = ref({});
const versionInfo = ref({});

const onUpdate = async () => {
  if (versionInfo.value.test && versionInfo.value.url) {
    const isConfirm = await (new Promise((resolve) => {
      uni.showModal({
        title: "提示",
        content: "新版本为测试版本，可能存在不稳定的情况，非测试人员不建议更新",
        confirmText: "更新",
        cancelText: "返回",
        success({ confirm }) {
          resolve(confirm);
        },
      });
    }));
    if (!isConfirm) {
      return;
    }
  }
  if (versionInfo.value.url) {
    uni.showModal({
      title: i18n("modalTitleAlert"),
      content: i18n("updateHint"),
      confirmText: i18n("modalBtnConfirm"),
      showCancel: false,
    });
    window.android?.update?.(versionInfo.value.url);
  } else {
    uni.showModal({
      title: i18n("modalTitleAlert"),
      content: i18n("noNewVersionHint"),
      confirmText: i18n("modalBtnConfirm"),
      showCancel: false,
    });
  }
};

const toProtocol = () => {
  // uni.navigateTo({
  //   url: `/pages/protocol-preview/index?title=${i18n("pageUserProtocol")}&content=protocol_`,
  // });
};

const toPrivacy = () => {
  // uni.navigateTo({
  //   url: `/pages/protocol-preview/index?title=${i18n("pagePrivacy")}&content=privacy_`,
  // });
};

const openEmail = () => {
  window.android?.openEmail?.(emailAddress.value);
};
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>