import { Request } from "./request.js";
import { i18n } from "@/locales/_i18n.js";

const flavor = window.android?.getFlavorName?.() || "businesszghn";
let flavorUrl = "http://47.102.109.244:8787";
switch (flavor) {
case "businessjwtl":
  flavorUrl = "https://lvlockjwtl.lcxxjs.cn";
  break;
case "businesswztt":
  flavorUrl = "https://tieta.lcxxjs.cn";
  break;
case "businesshar":
  flavorUrl = "https://www.setpower.cn";
  break;
case "businesstwhd":
  flavorUrl = "https://time-wave.cn";
  break;
case "businesszghn":
  flavorUrl = "http://gdyd.lcxxjs.cn";
  break;
}
Request.baseUrl = flavorUrl;
// Request.baseUrl = "http://192.168.100.21:3000/proxy";

// 提交 Modal 提示
class RequestWithModal extends Request {
  async send(content) {
    if (content) {
      await new Promise((resolve) => {
        uni.showModal({
          title: i18n.global.tm("modalTitleAlert"),
          content,
          confirmText: i18n.global.tm("modalBtnConfirm"),
          cancelText: i18n.global.tm("modalBtnCancel"),
          success: ({ confirm }) => {
            if (confirm) {
              super.send();
              resolve();
            }
          },
          fail: () => {
            resolve();
          },
        });
      });
    } else {
      super.send();
    }
  }

  async getData(successHint, failHint, showModalOnError = false) {
    try {
      const data = await super.getData();
      const code = data.code;
      if (successHint && failHint) {
        await new Promise((resolve) => {
          uni.showModal({
            title: i18n.global.tm("modalTitleAlert"),
            content: code === 200 ? successHint : failHint,
            confirmText: i18n.global.tm("modalBtnConfirm"),
            showCancel: false,
            complete() {
              resolve();
            },
          });
        });
      }
      return data;
    } catch (err) {
      if (showModalOnError) {
        uni.showModal({
          title: i18n.global.tm("modalTitleAlert"),
          content: err.message,
          confirmText: i18n.global.tm("modalBtnConfirm"),
          showCancel: false,
        });
      }
      throw err;
    }
  }
}

// 网络连接测试
export class NetWorkTestApi extends RequestWithModal {
  constructor() {
    super("/user/me");
    this.method(Request.methods.POST);
  }

  async test(token, showModalOnError = false) {
    this.token(token);
    await this.send();
    try {
      await this.getData(null, null, showModalOnError);
      return true;
    } catch (err) {
      return false;
    }
  }
}

// token 有效性判断
class RequestWithTokenValidCheck extends RequestWithModal {
  async getData(successHint, failHint, showModalOnError) {
    const data = await super.getData(successHint, failHint, showModalOnError);
    const code = data.code;
    if (code === -1) {
      uni.showModal({
        title: i18n.global.tm("modalTitleAlert"),
        content: i18n.global.tm("modalContentTokenInvalid"),
        confirmText: i18n.global.tm("modalBtnConfirm"),
        showCancel: false,
        success: () => {
          uni.removeStorageSync("token");
          uni.reLaunch({
            url: "/pages/login/index",
          });
        },
      });
      throw new Error("Invalid Token");
    } else {
      return data;
    }
  }
}

// 登录
export class LoginApi extends RequestWithModal {
  constructor() {
    super("/auth/login");
    this.method(Request.methods.POST);
  }
}

// 用户信息
export class UserInfoApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/auth/me");
    this.method(Request.methods.POST);
  }
}

// 修改用户信息
export class UserInfoUpdateApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/user/update");
    this.method(Request.methods.POST);
  }
}

// 注册
export class RegisterApi extends RequestWithModal {
  constructor() {
    super("/user/register");
    this.method(Request.methods.POST);
  }
}

// 版本信息
export class VersionApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/apkversion/index");
    this.method(Request.methods.POST);
  }
}

// 首页统计数据
export class HomeStatisticApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/data/countApp");
    this.method(Request.methods.POST);
  }
}

// 区域列表
export class AreaListApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/devicecategory/index");
    this.method(Request.methods.GET);
  }
}

// 部门列表
export class DepartmentListApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/department/index");
    this.method(Request.methods.GET);
  }
}

// 设备列表
export class DeviceListApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/device/index");
    this.method(Request.methods.POST);
  }
}

// 设备详情
export class DeviceDetailApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/device/show");
    this.method(Request.methods.GET);
  }
}

// 设备详情（使用 entity_id 获取）
export class DeviceDetailByEntityIdApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/device/showEntityId");
    this.method(Request.methods.GET);
  }
}

// 创建设备
export class DeviceCreateApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/device/create");
    this.method(Request.methods.POST);
  }
}

// 修改设备
export class DeviceUpdateApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/device/update");
    this.method(Request.methods.POST);
  }
}

// 删除设备
export class DeviceDeleteApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/device/delete");
    this.method(Request.methods.POST);
  }
}

// 同步所有设备 / 获取本地存储设备
export class LocalDeviceListApi extends DeviceListApi {
  #STORE_KEY = "localDeviceList";

  constructor() {
    super();
  }

  async #getList(token, cId) {
    const datas = [];
    const pageSize = 50;
    for (let t = 1; t <= 100; t++) {
      this.token(token).data({
        page: t,
        page_size: pageSize,
        c_id: cId,
      });
      await this.send();
      const result = await this.getData();
      const list = result.data?.list || [];
      datas.push(list);
      if (list.length < pageSize) {
        break;
      }
    }
    return datas.flat(1);
  }

  async sync(token, cId) {
    const data = await this.#getList(token, cId);
    uni.setStorageSync(this.#STORE_KEY, data);
  }

  load(lockId) {
    const datas = uni.getStorageSync(this.#STORE_KEY);
    if (lockId) {
      return datas.find((item) => item.entity_id === lockId);
    } else {
      return datas;
    }
  }
}

// 操作日志列表
export class OperationLogListApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/openlog/list");
    this.method(Request.methods.GET);
  }
}

// 创建操作日志
export class OperationLogCreateApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/openlog/add");
    this.method(Request.methods.POST);
  }
}

// 设备申请列表
export class DeviceApplyListApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/approval/list");
    this.method(Request.methods.GET);
  }
}

// 创建设备申请
export class DeviceApplyCreateApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/approval/create");
    this.method(Request.methods.POST);
  }
}

// 修改申请状态
export class DeviceApplyUpdateApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/approval/isPass");
    this.method(Request.methods.POST);
  }
}

// 告警信息列表
export class AlertInfoListApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/deviceAlarm/list");
    this.method(Request.methods.POST);
  }
}

// 上传开锁图片
export class UploadUnlockPhotoApi extends RequestWithTokenValidCheck {
  constructor() {
    super("/openLog/editPic");
    this.method(Request.methods.POST);
  }
  
  token(token) {
    super.token(token);
    this._token = token;
    return this;
  }
  
  upload(filePath, success = () => null, fail = () => null) {
    uni.uploadFile({
      url: `${Request.baseUrl}/upload/lockOpenLogImg`,
      filePath,
      header: {
        "Authorization": this._token,
      },
      name: "file",
      success,
      fail,
    });
  }
}