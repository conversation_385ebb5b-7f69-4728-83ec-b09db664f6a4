<template>
  <view class="page">
    <view class="top">
      <template v-if="formModelType === 'register'">
        <view class="title">
          注册账号
        </view>
      </template>
      <template v-else>
        <view class="title">
          欢迎使用 NFC 无源锁
        </view>
        <view class="title-sub">
          <text>还没有账号？</text>
          <text class="register" @click="toRegister">
            立即注册
          </text>
        </view>
      </template>
    </view>
    <view class="form">
      <view v-if="formModelType === 'password'">
        <view class="form-item">
          <uv-input v-model="formModel.phone" type="number" placeholder="请输入手机号" border="none" maxlength="11" :custom-style="{ backgroundColor: '#f6f6f6', padding: '24rpx' }" />
        </view>
        <view class="form-item">
          <uv-input v-model="formModel.password" type="password" placeholder="请输入密码" border="none" maxlength="16" :custom-style="{ backgroundColor: '#f6f6f6', padding: '24rpx' }" />
        </view>
        <view class="btns">
          <view class="first" @click="onPasswordLogin">
            登录
          </view>
          <!-- <view class="second" @click="toVerifyType">短信验证</view> -->
        </view>
      </view>
      <!-- <view v-else-if="formModelType === 'verify'">
        <view class="form-item">
          <uv-input type="number" placeholder="请输入手机号" border="none" maxlength="11" v-model="formModel.phone" :custom-style="{ backgroundColor: '#f6f6f6', padding: '24rpx' }"></uv-input>
        </view>
        <view class="form-item">
          <uv-input type="number" placeholder="请输入验证码" border="none" maxlength="6" v-model="formModel.verifyCode" :custom-style="{ backgroundColor: '#f6f6f6', padding: '24rpx' }">
            <template #suffix>
              <view>
                <uv-code ref="vcode" :seconds="60" start-text="获取验证码" end-text="重新获取" change-text="x 秒后重试" :keep-running="true" @change="onVerifyCodeChange"></uv-code>
                <text class="verify-code" :disabled="!vcode?.canGetCode" @click="getVerifyCode">{{ verifyCodeTips }}</text>
              </view>
            </template>
          </uv-input>
        </view>
        <view class="btns">
          <view class="first" @click="onVerifyLogin">登录</view>
          <view class="second" @click="toPasswordType">密码验证</view>
        </view>
      </view> -->
      <view v-else-if="formModelType === 'register'">
        <view class="form-item">
          <uv-input v-model="formModel.phone" type="number" placeholder="请输入手机号" border="none" maxlength="11" :custom-style="{ backgroundColor: '#f6f6f6', padding: '24rpx' }" />
        </view>
        <view class="form-item">
          <uv-input v-model="formModel.password" type="password" placeholder="请输入密码" border="none" maxlength="16" :custom-style="{ backgroundColor: '#f6f6f6', padding: '24rpx' }" />
        </view>
        <!-- <view class="form-item">
          <uv-input type="number" placeholder="请输入验证码" border="none" maxlength="6" v-model="formModel.verifyCode" :custom-style="{ backgroundColor: '#f6f6f6', padding: '24rpx' }">
            <template #suffix>
              <view>
                <uv-code ref="vcode" :seconds="60" start-text="获取验证码" end-text="重新获取" change-text="x 秒后重试" :keep-running="true" @change="onVerifyCodeChange"></uv-code>
                <text class="verify-code" :disabled="!vcode?.canGetCode" @click="getVerifyCode">{{ verifyCodeTips }}</text>
              </view>
            </template>
          </uv-input>
        </view> -->
        <view class="btns">
          <view class="first" @click="onRegister">
            注册
          </view>
          <view class="second" @click="toPasswordType">
            密码验证
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { onShow } from "@dcloudio/uni-app";
import { ref } from "vue";
import { LoginApi, RegisterApi, UserInfoApi } from "@/utils/api.js";
import { uniModal } from "@/utils/util.js";

const formModel = ref({});
const formModelType = ref("password");
const vcode = ref(null);
const verifyCodeTips = ref("");

const formModelVerify = () => {
  const { phone, password, verifyCode } = formModel.value;
  const phoneRegex = /^1[3456789]\d{9}$/;
  let isComplete = (formModelType.value === "password" && phone && password);
  isComplete ||= (formModelType.value === "verify" && phone && verifyCode);
  isComplete ||= (formModelType.value === "register" && phone && password);
  if (isComplete) {
    if (phoneRegex.test(phone)) {
      return true;
    } else {
      uniModal("请输入正确的手机号");
    }
  } else {
    uniModal("请填写完整的内容");
  }
  return false;
};

const onLoginProcess = async (params) => {
  if (formModelVerify()) {
    const api = new LoginApi().data(params);
    await api.send();
    const result = await api.getData(null, null, true);
    if (result.code === 200) {
      uni.setStorageSync("token", `Bearer ${result.data.token}`);
      uni.navigateTo({
        url: "/pages/index/index",
      });
    } else {
      uniModal("登录失败");
    }
  }
};

const onPasswordLogin = async () => {
  const { phone, password } = formModel.value;
  onLoginProcess({
    phone,
    password,
    // login_type: 2,
  });
};

// const onVerifyLogin = async () => {
//   const { phone, verifyCode } = formModel.value;
//   onLoginProcess({
//     phone,
//     password: verifyCode,
//     login_type: 1,
//   });
// };

const onRegister = async () => {
  const { phone, password } = formModel.value;
  if (formModelVerify()) {
    const api = new RegisterApi().data({
      username: phone.replace(/(\d{3})\d*(\d{4})/, "$1****$2"),
      password,
      phone,
    });
    await api.send();
    const result = await api.getData();
    if (result.code === 200) {
      uniModal("注册成功");
      formModel.value = {};
      formModelType.value = "password";
    } else {
      if (result.code === 500) {
        uniModal("该账号已注册");
      } else {
        uniModal("注册失败");
      }
    }
  }
};

const toPasswordType = () => {
  formModelType.value = "password";
};

const toVerifyType = () => {
  formModelType.value = "verify";
};

const toRegister = () => {
  formModelType.value = "register";
};

// const getVerifyCode = async () => {
//   if (vcode.value.canGetCode) {
//     const { phone } = formModel.value;
//     const phoneRegex = /^1[3456789]\d{9}$/;
//     if (phone && phoneRegex.test(phone)) {
//       const api = new VerifyCodeApi().data({ phone });
//       await api.send();
//       api.getData();
//       vcode.value.start();
//     } else {
//       uniModal("请输入正确的手机号")
//     }
//   }
// };

const onVerifyCodeChange = (text) => {
  verifyCodeTips.value = text;
};

onShow(async () => {
  const token = uni.getStorageSync("token");
  if (token) {
    const api = new UserInfoApi().token(token);
    await api.send();
    const result = await api.getData();
    formModel.value.phone = result.data.phone;
  }
});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>