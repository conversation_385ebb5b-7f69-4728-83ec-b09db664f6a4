<template>
  <my-drawer
    :title="i18n('componentNameAddMember')"
    :height="900"
    :open="show"
    @close="onBack"
  >
    <template #content>
      <view class="form">
        <view class="form-box">
          <view class="form-item">
            <uv-input
              v-model="formModel.phone"
              type="number"
              :placeholder="i18n('placeholderPhone')"
              border="none"
              maxlength="11"
              :custom-style="{ backgroundColor: '#f6f6f6', padding: '24rpx' }"
            />
          </view>
        </view>
        <view class="btns">
          <view @click="onSubmit">
            {{ i18n("btnConfirm") }}
          </view>
        </view>
      </view>
    </template>
  </my-drawer>
</template>

<script setup>
import { ref, watch } from "vue";
import { useI18n } from "vue-i18n";

import MyDrawer from "../drawer/index.vue";

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  form: {
    type: Object,
    default: () => ({}),
  },
});

const emits = defineEmits(["submit", "update:show", "update:form"]);

const { tm: i18n } = useI18n();
const formModel = ref({});

const onSubmit = (value) => {
  emits("update:form", formModel.value);
  emits("submit");
};

const onBack = () => {
  emits("update:show", false);
};

watch(() => props.show, () => {
  formModel.value = props.form;
});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>