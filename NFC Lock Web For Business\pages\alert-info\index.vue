<template>
  <view class="page">
    <base-layout
      page-title="告警信息"
      :show-back="true"
      :show-tab="false"
      @user-info="(info) => userInfo = info"
    >
      <template #content>
        <view class="content-box">
          <view class="picker">
            <uni-datetime-picker
              v-model="selectedDates"
              :border="false"
              type="daterange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              range-separator="至"
              @change="onDateChange"
            />
          </view>

          <scroll-view v-if="alertInfoList.length" scroll-y class="list-box" @scrolltolower="onAlertInfoListToLower">
            <view
              v-for="(item, index) in alertInfoList"
              :key="index"
              class="device-info"
              :type="['error', 'normal', 'warning'][item.alarm_type - 1]"
              @click="toMap(item)"
            >
              <view class="main-title">
                {{ item.title }}
              </view>
              <view class="wrap">
                <text class="title">
                  设备名称
                </text>
                <text class="text">
                  {{ item.device_name }}
                </text>
              </view>
              <view class="wrap">
                <text class="title">
                  告警时间
                </text>
                <text class="text">
                  {{ item.alarm_time }}
                </text>
              </view>
              <view class="wrap">
                <text class="title">
                  开锁时间
                </text>
                <text class="text">
                  {{ item.action_time }}
                </text>
              </view>
              <view class="wrap">
                <text class="title">
                  操作人员
                </text>
                <text class="text">
                  {{ item.username }}
                </text>
              </view>
              <view class="wrap">
                <text class="title">
                  状态
                </text>
                <text class="text highlight">
                  {{ deviceOpenStatus[item.device_status]?.text }}
                </text>
              </view>
            </view>
            <br>
          </scroll-view>
          <uv-empty v-else class="empty-box" name="list" icon-size="110" text-size="15" text="暂无数据" />
        </view>
      </template>
    </base-layout>
  </view>
</template>

<script setup>
import { ref, watch } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { AlertInfoListApi } from "@/utils/api.js";
import { deviceOpenStatus } from "@/pages/device/data.js";

import BaseLayout from "@/components/base-layout/index.vue";

const userInfo = ref({});
const alertInfoList = ref([]);
const currentPage = ref(1);
const pageSize = ref(50);
const total = ref(0);
const selectedDates = ref([]);

const initDatas = async () => {
  const api = new AlertInfoListApi().token(userInfo.value.token).data({
    start_time: selectedDates.value[0] || "",
    end_time: selectedDates.value[1] || "",
    page: currentPage.value,
    page_size: pageSize.value,
  });
  await api.send();
  const result = await api.getData();
  alertInfoList.value = [...alertInfoList.value, ...result.data.list];
  total.value = result.data.total;
};

const onAlertInfoListToLower = () => {
  if (alertInfoList.value.length < total.value) {
    currentPage.value += 1;
    initDatas();
  }
};

const onDateChange = (e) => {
  selectedDates.value = e;
  currentPage.value = 1;
  alertInfoList.value = [];
  initDatas();
};

const toMap = (item) => {
  uni.navigateTo({
    url: `/pages/map/index?lat=${item.lt}&lng=${item.lg}`,
  });
};

watch(() => userInfo.value, () => {
  initDatas();
});

onLoad(() => {

});

onShow(() => {

});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>