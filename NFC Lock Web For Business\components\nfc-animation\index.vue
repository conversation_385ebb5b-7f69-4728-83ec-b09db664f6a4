<template>
  <my-drawer
    title="NFC"
    :height="650"
    :open="show"
    @close="onBack"
  >
    <template #content>
      <view class="container">
        <view class="nfc-box">
          <image
            class="nfc"
            src="../../static/nfc.png"
            mode="aspectFit"
          />
          <view class="title">
            {{ i18n("nfcHint") }}
          </view>
        </view>
      </view>
    </template>
  </my-drawer>
</template>

<script setup>
import { useI18n } from "vue-i18n";

import MyDrawer from "../drawer/index.vue";

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["back"]);

const { tm: i18n } = useI18n();

const onBack = () => {
  emits("back");
};
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>