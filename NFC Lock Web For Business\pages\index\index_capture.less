.page {
  width: 100%;
  height: 100%;
  background-image: url("../../static/index_back.png");
  background-size: cover;
  background-position: top;
  background-repeat: no-repeat;
  
  .content-box {
    height: 100%;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-direction: column;
    padding: 20rpx;
  
    .spinner {
      width: 500rpx;
      height: 500rpx;
      background: linear-gradient(to bottom, #00000055, #00000001);
      border-radius: 999rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      
      .progress {
        position: absolute;
      }
      
      .circle {
        position: relative;
        z-index: 9;
        width: 360rpx;
        height: 360rpx;
        background-image: url("../../static/new_circle_lock.png");
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        
        .percent {
          margin-top: 62%;
          color: white;
          font-size: 56rpx;
          text-align: center;
        }
      }
    }
  
    .down-box {
      width: 100%;
      height: 600rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      
      .detect-prompt {
        color: #888888;
        font-size: 50rpx;
        font-weight: bold;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      
      .device-infos {
        height: 100rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-direction: column;
        
        .device-id {
          color: #333333;
          font-size: 26rpx;
          font-weight: bold;
        }
        
        .device-name {
          color: #333333;
          font-size: 36rpx;
          font-weight: bold;
        }
      }
      
      .copy {
        font-size: 85%;
        color: #3080F7;
        margin-top: 10rpx;
        text-decoration: underline;
      }
        
      .btn-group {
        width: 100%;
        display: grid;
        grid-template-rows: repeat(2, 1fr);
        grid-template-columns: repeat(2, 1fr);
        gap: 30rpx;
        
        .btn {
          width: 100%;
          height: 138rpx;
          background-image: url("../../static/btn_operation_back.png");
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          border-radius: 12rpx;
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;
          
          &[selected="true"] {
            background-image: url("../../static/btn_operation_back_selected.png");
            
            .text {
              color: white;
            }
          }
          
          .icon {
            width: 80rpx;
            height: 80rpx;
            background-size: contain;
            background-position: center center;
            background-repeat: no-repeat;
          }
          
          .text {
            color: #ffffff88;
            font-size: 36rpx;
            font-weight: bold;
          }
        }
        
        .btn-unlock {
          color: white;
        
          &[selected="true"] {
            .icon {
              background-image: url("../../static/new_lock_open_white.png");
            }
          }
        }
        
        .btn-lock {
          color: white;
      
          &[selected="true"] {
            .icon {
              background-image: url("../../static/new_lock_close_white.png");
            }
          }
        }
        
        .btn-capture, .btn-record {
          color: white;
          
          .text {
            color: white;
          }
        
          &[selected="true"] {
            .icon {
              background-image: url("../../static/new_lock_close_white.png");
            }
          }
        }
      }
    }
  }
}
