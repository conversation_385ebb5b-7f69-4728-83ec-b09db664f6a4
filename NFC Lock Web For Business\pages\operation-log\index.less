.page {
  width: 100%;
  height: 100%;

  :deep(.page-content) {
    padding: 0rpx;
  }

  .content-box {
    height: 100%;
    display: flex;
    flex-direction: column;

    .picker {
      padding: 6rpx 30rpx;
      margin-bottom: 20rpx;
    }

    .empty-box {
      height: 0rpx;
      flex: 1;
    }

    .list-box {
      flex: 1;
      height: 0;

      .list-item {
        background-color: #ffffff;
        padding: 12rpx 20rpx;
        border-radius: 12rpx;
        box-shadow: 0rpx 0rpx 40rpx #3080F733;
        margin: 0rpx 32rpx;
        margin-top: 20rpx;

        .device-name {
          font-weight: bold;
          color: #3080F7;
        }

        .wrap-1 {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 8rpx;

          .name {
            font-weight: bold;
          }

          .status {
            color: #ffffff;
            background-color: #3080F7;
            font-size: 85%;
            padding: 6rpx 16rpx;
            border-radius: 2000rpx;

            &.success {
              background-color: #8bc34a;
            }

            &.error {
              background-color: #ff5252;
            }
          }
        }

        .wrap-2 {
          color: #888888;
          font-size: 85%;
          margin-top: 8rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        
        .wrap-3 {
          margin-top: 8rpx;
          font-size: 85%;
          display: flex;
          align-items: center;
          
          .btn {
            padding: 5rpx 8px;
            margin-right: 14rpx;
            color: white;
            border-radius: 99rpx;
        
            &.blue {
              background-color: #3080F7;
            }
            
            &.green {
              background-color: #8bc34a;
            }
          }
        }
      }
    }
  }
}
