.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .nfc-box {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    
    .nfc {
      width: 240rpx;
      height: 240rpx;
    }
    
    .title {
      margin-top: 40rpx;
      color: #666666;
      font-weight: bold;
      text-align: center;
      white-space: pre-wrap;
    }
  }
  
  .btns {
    :nth-child(n) {
      margin-top: 16rpx;
      padding: 16rpx 0rpx;
      color: white;
      background-color: #007bff;
      font-size: 80%;
      border: 2rpx solid #007bff;
      border-radius: 6rpx;
      display: flex;
      justify-content: center;
    }
    
    .back {
      color: #007bff;
      background-color: transparent;
    }
  }
}