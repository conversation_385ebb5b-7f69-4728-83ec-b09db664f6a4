<template>
  <view class="page">
    <base-layout v-model:reload-user-info="reloadUserInfo" page-title="" :selected-index="4" @user-info="(info) => userInfo = info" @version-info="(info) => versionInfo = info">
      <template #content>
        <view class="content-box">
          <view class="user-background">
            <view class="user-info">
              <view class="avatar" />
              <view class="username">
                {{ userInfo.username }}
              </view>
              <uv-icon class="icon" name="edit-pen" size="24" color="white" @click="editUserInfo('username')" />
            </view>
            <view class="option-list">
              <view class="item">
                <view class="text">
                  所属部门
                </view>
                <view class="value">
                  {{ userInfo.dept_name }}
                </view>
              </view>
              <view class="item" @click="editUserInfo('password')">
                <view class="text">
                  修改密码
                </view>
              </view>
              <view class="item" @click="toAbout">
                <uv-badge v-if="versionInfo.url" :absolute="true" :offset="[26, 40]" :is-dot="true" type="error" />
                <view class="text">
                  关于我们
                </view>
                <uv-icon class="icon" name="arrow-right" size="16" />
              </view>
              <view class="item">
                <view class="text">
                  操作说明
                </view>
                <uv-icon class="icon" name="arrow-right" size="16" />
              </view>
            </view>
            <view class="btn-logout" @click="logout">
              退出登录
            </view>
          </view>
        </view>
      </template>
    </base-layout>
    <my-edit-user-info v-model:form="userInfoForm" :show="showEditUserInfo" :type="editUserInfoType" @submit="onSubmit" @close="showEditUserInfo = false" />
  </view>
</template>

<script setup>
import { ref, watch } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { uniModal } from "@/utils/util.js";
import { UserInfoUpdateApi, DepartmentListApi, AreaListApi } from "@/utils/api.js";

import BaseLayout from "@/components/base-layout/index.vue";
import MyEditUserInfo from "@/components/edit-user-info/index.vue";

const userInfo = ref({});
const versionInfo = ref({});
const reloadUserInfo = ref(false);
const userInfoForm = ref({});
const showEditUserInfo = ref(false);
const editUserInfoType = ref("");
const departmentList = ref([]);
const areaList = ref([]);

const initDepartmentList = async () => {
  const api = new DepartmentListApi().token(userInfo.value.token).data({});
  await api.send();
  const result = await api.getData();
  departmentList.value = result.data;
  console.log(departmentList.value);
};

const initAreaList = async () => {
  const api = new AreaListApi().token(userInfo.value.token).data({});
  await api.send();
  const result = await api.getData();
  areaList.value = result.data;
  console.log(areaList.value);
};

const editUserInfo = (type) => {
  userInfoForm.value = {};
  if (type === "username") {
    userInfoForm.value.username = userInfo.value.username;
  }
  editUserInfoType.value = type;
  showEditUserInfo.value = true;
};

const onSubmit = async () => {
  const { username, password, rePassword } = userInfoForm.value;
  const hasUsername = editUserInfoType.value === "username" && username;
  const hasPassword = editUserInfoType.value === "password" && password && rePassword;
  const formData = {
    id: userInfo.value.id,
  };
  if (hasUsername) {
    formData.username = username;
  }
  if (hasPassword) {
    if (password !== rePassword) {
      uniModal("两次输入的密码不同，请重新输入");
      return;
    }
    formData.password = password;
  }
  if (hasUsername || hasPassword) {
    const api = new UserInfoUpdateApi().token(userInfo.value.token).data(formData);
    await api.send("确定要修改吗？");
    const result = await api.getData("修改成功", "修改失败");
    if (result.code === 200) {
      reloadUserInfo.value = true;
      showEditUserInfo.value = false;
    }
  } else {
    uniModal("请输入修改信息");
  }
};

const toAbout = () => {
  uni.navigateTo({
    url: "/pages/about-us/index",
  });
};

const logout = () => {
  uniModal("确定要退出登录吗？", true, (res) => {
    if (res.confirm) {
      uni.clearStorage();
      uni.reLaunch({
        url: "/pages/index/index",
      });
    }
  });
};

watch(() => userInfo.value, async () => {
  // await initDepartmentList();
  // await initAreaList();
});

onLoad(() => {

});

onShow(() => {

});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>