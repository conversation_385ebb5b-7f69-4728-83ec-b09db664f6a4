const defaultVarientConfig = {
  register: false,
  captureIndex: false,
  bioIndex: false,
  reLoginWhenEnter: false,
  // Custom Page
  customRequirePageZGHN: false,
};

export const getVarientConfig = () => {
  const flavor = window.android?.getFlavorName?.();
  switch (flavor) {
  case "business":
    return {
      ...defaultVarientConfig,
      bioIndex: true,
    };
  case "businesswztt":
    return {
      ...defaultVarientConfig,
      register: true,
    };
  case "businesshar":
    return {
      ...defaultVarientConfig,
      captureIndex: true,
    };
  case "businesszghn":
    return {
      ...defaultVarientConfig,
      customRequirePageZGHN: true,
    };
  default:
    return defaultVarientConfig;
  }
};
