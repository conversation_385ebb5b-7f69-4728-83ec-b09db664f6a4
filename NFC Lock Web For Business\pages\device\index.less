.page {
  width: 100%;
  height: 100%;
  
  .content-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20rpx 0rpx;
  
    .search-box {
      width: 100%;
      height: 70rpx;
      background-color: #EEEEEE;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 2000rpx;
      color: #888888;
    }

    .empty-box {
      height: 0rpx;
      flex: 1;
    }
  
    .device-list {
      height: 0rpx;
      flex: 1;
      margin-top: 20rpx;
  
      .list-item {
        display: flex;
        align-items: center;
        padding: 20rpx 0rpx;
        border-bottom: 2rpx solid #eeeeee;
  
        .icon-box {
          width: 80rpx;
          height: 80rpx;
          border-radius: 12rpx;
          background-color: #3080F7;
          display: flex;
          justify-content: center;
          align-items: center;
        
          .icon {
            width: 60rpx;
            height: 60rpx;
            background-size: contain;
            background-position: center center;
            background-repeat: no-repeat;
            background-image: url("../../static/new_lock_close_white.png");
          }
        }
  
        .infos {
          flex: 1;
          margin-left: 28rpx;

          .wrap-1 {
            display: flex;
            justify-content: space-between;
            align-items: center;
  
            .name {
              font-weight: bold;
            }
  
            .status {
              color: #ffffff;
              background-color: #8bc34a;
              font-size: 85%;
              padding: 6rpx 16rpx;
              border-radius: 2000rpx;

              &[status="success"] {
                background-color: #8bc34a;
              }

              &[status="error"] {
                background-color: #ff5252;
              }

              &[status="normal"] {
                background-color: #8bc34a;
              }

              &[status="unknown"] {
                background-color: #888888;
              }
            }
          }
  
          .address {
            color: #888888;
            font-size: 80%;
            margin-top: 4rpx;
          }
        }
      }
    }
  }
}