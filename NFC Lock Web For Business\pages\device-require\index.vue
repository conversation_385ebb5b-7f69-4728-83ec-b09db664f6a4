<template>
  <view class="page">
    <base-layout
      page-title="申请设备"
      :show-back="true"
      :show-tab="false"
      @user-info="(info) => userInfo = info"
    >
      <template #content>
        <view class="content-box">
          <view class="device-info">
            <view class="wrap">
              <text class="title">
                设备名称
              </text>
              <text class="text">
                {{ deviceInfo.name }}
              </text>
            </view>
            <view class="wrap">
              <text class="title">
                设备ID
              </text>
              <text class="text">
                {{ deviceInfo.entity_id }}
              </text>
            </view>
            <view class="wrap">
              <text class="title">
                位置
              </text>
              <text class="text">
                {{ deviceInfo.addr }}
              </text>
            </view>
            <view class="wrap">
              <text class="title">
                申请人
              </text>
              <text class="text">
                {{ userInfo.username }}
              </text>
            </view>
          </view>
          <view class="form">
            <view class="form-item">
              <view class="label">
                申请时限
              </view>
              <picker
                class="value"
                :value="timeLimit"
                :range="timeLimitList"
                @change="handleTimeLimitChange"
              >
                <view>{{ timeLimitList[timeLimit] || "请选择申请时限" }}</view>
              </picker>
              <uv-icon
                name="arrow-right"
                size="16"
                color="#999"
              />
            </view>
            <!-- <view class="form-item">
              <view class="label">
                作业类型
              </view>
              <picker
                class="value"
                :value="workType"
                :range="workTypeList"
                @change="handleWorkTypeChange"
              >
                <view>{{ workTypeList[workType] || "请选择作业类型" }}</view>
              </picker>
              <uv-icon
                name="arrow-right"
                size="16"
                color="#999"
              />
            </view> -->
            <view class="form-item">
              <view class="label">
                备注
              </view>
              <uv-input
                v-model="remark"
                class="value"
                placeholder="请输入备注"
                border="none"
              />
            </view>
          </view>
          <view class="btn-submit" @click="onSubmit">
            提交申请
          </view>
        </view>
      </template>
    </base-layout>
  </view>
</template>

<script setup>
import { ref, watch } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { expireTimes } from "@/pages/require/data.js";
import { uniModal } from "@/utils/util.js";

import BaseLayout from "@/components/base-layout/index.vue";
import { DeviceApplyCreateApi, DeviceDetailApi } from "@/utils/api";

const userInfo = ref({});
const deviceId = ref(null);
const deviceInfo = ref({});
const timeLimitList = Object.values(expireTimes);
// const workTypeList = ["抢修", "施工", "巡检", "其它"];
const timeLimit = ref(null);
// const workType = ref(null);
const remark = ref("");

const handleTimeLimitChange = (e) => {
  timeLimit.value = e.detail.value;
};

// const handleWorkTypeChange = (e) => {
//   workType.value = e.detail.value;
// };

const initDatas = async () => {
  const api = new DeviceDetailApi().token(userInfo.value.token).data({
    id: deviceId.value,
  });
  await api.send();
  const result = await api.getData();
  deviceInfo.value = result.data;
};

const onSubmit = () => {
  if (timeLimit.value === null) {
    uniModal("请选择申请时限", false);
  } else {
    uniModal("确定要提交申请吗？", true, async (result) => {
      if (result.confirm) {
        const api = new DeviceApplyCreateApi().token(userInfo.value.token).data({
          device_id: deviceId.value,
          expires_type: timeLimit.value + 1,
          // work_type: workType.value + 1,
          reason: remark.value,
        });
        await api.send();
        const result = await api.getData();
        if (result.code === 200) {
          uniModal("提交成功", false, () => {
            uni.navigateBack({
              delta: 1,
            });
          });
        } else {
          uniModal(result.msg);
        }
      }
    });
  }
};

watch(() => userInfo.value, () => {
  initDatas();
});

onLoad((options) => {
  deviceId.value = options.id;
});

onShow(() => {

});
</script>

<style lang="less" scoped>
  @import "./index.less";
</style>