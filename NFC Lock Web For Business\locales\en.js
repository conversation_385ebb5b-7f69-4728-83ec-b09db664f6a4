import { QaH_en } from "./QaH_en.js";
import { protocol_en } from "./protocol_en.js";
import { privacy_en } from "./privacy_en.js";

export const en = {
  "registerTitle": "Sign up",
  "loginTitle": "Welcome to login",
  "loginSubTitle": "No account yet?",
  "loginSubToRegister": "Sign up",
  "waitVerifyCode": "x s",
  "unlockHint": "Select an option and bring your phone close to the device",
  "nfcExceptionHint": "The connection is lost,\nplease try again",
  "unlockVerifyHint": "Verifying device...",
  "noLockInfoHint": "Device not authorized",
  "unlockVerifyFailedHint": "Authentication failed",
  "unlockChargingHint": "Device charging...",
  "unlockChargingTimeoutHint": "Charging timeout",
  "unlockingHint": "Unlocking...",
  "lockingHint": "Locking...",
  "unlockSucceededHint": "Unlock successfully",
  "lockSucceededHint": "Lock successfully",
  "unlockFailedHint": "Unlock failed",
  "lockFailedHint": "Lock failed",
  "lockNameHint": "Device name: ",
  "syncSucceededHint": "{v0} local operation records have been synchronized",
  "syncFailedHint": "Operation record synchronization failed",
  "deviceInitializedHint": "The device has been initialized and cannot be added again",
  "deviceIncorrectHint": "Incorrect device",
  "nfcHint": "Please bring your phone close to the device. \nIf there is no response, please move your phone away and then bring it close to the device again.",
  "versionCode": "Version code",
  "updateHint": "Updating, please wait...",
  "newVersionHint": "New version available",
  "noNewVersionHint": "Currently the latest version",
  
  "pageNameIndex": "Home",
  "pageNameDeviceManage": "Device",
  "pageNameMemberManage": "Member",
  "pageNameMy": "My",
  "pageLanguageSetting": "Language Setting",
  "pageAboutUs": "About Us",
  "pageHelp": "Questions and Help",
  "pageNfcLocation": "NFC Location Reference",
  "pageUserProtocol": "User agreement",
  "pagePrivacy": "Privacy policy",
  
  "componentNameAddDevice": "Add New Device",
  "componentNameEditDevice": "Edit Device Information",
  "componentNameAddMember": "Add New Member",
  "componentNameEditUsername": "Edit Nickname",
  "componentNameEditPassword": "Edit Password",
  "componentNameEditDeviceName": "Edit Device Name",
  "componentNameOperationRecord": "Operation Record",
  "componentNameSelectorDevice": "Authorization",
  "componentNameSelectorMember": "Authorization",
  
  "placeholderUsername": "Enter your username",
  "placeholderPhone": "Enter your phone number",
  "placeholderPassword": "Enter your password",
  "placeholderPasswordAgain": "Enter your password again",
  "placeholderPasswordAllowEmpty": "Enter the new password",
  "placeholderVerifyCode": "Enter the verification code",
  "placeholderSearch": "Enter keywords",
  "placeholderDeviceName": "Enter the device name",
  "placeholderRemark": "Enter remarks",
  "placeholderStartDate": "Start date",
  "placeholderEndDate": "End date",
  
  "btnLogin": "Login",
  "btnLogout": "Logout",
  "btnRegister": "Sign up",
  "btnEdit": "Edit",
  "btnDelete": "Delete",
  "btnConfirm": "Confirm",
  "btnCancel": "Cancel",
  "btnPasswordVerify": "Password verification",
  "btnSMSVerify": "SMS verification",
  "btnToLogin": "To login",
  "btnGetCode": "Get verification code",
  "btnRetryCode": "Get code again",
  "btnUnlock": "Unlock",
  "btnLock": "Lock",
  "btnAuthManage": "To authorize",
  "btnOperationRecord": "Records",
  "btnMyDevice": "My Devices",
  "btnAuthorizedToMe": "Authorized to me",
  "btnSelectAll": "Select all",
  "btnCancelSelection": "Deselect",
  "btnDeviceAuth": "Device authorization",
  "btnMemberAuth": "Member authorization",
  "btnCancelAuth": "Cancel authorization",
  "btnEditPassword": "Edit password",
  "btnLanguageSetting": "Language setting",
  "btnAboutUs": "About us",
  "btnHelp": "Questions and help",
  "btnFollowSystem": "Follow the system",
  "btnUpdate": "Version update",
  "btnUserProtocol": "User agreement",
  "btnPrivacy": "Privacy policy",
  "btnContactUs": "Contact us",
  "btnNfcLocation": "NFC location reference",
  
  "modalTitleAlert": "Alert",
  "modalContentIncorrectPhone": "Please enter the correct phone number",
  "modalContentNotCompletedForm": "Please enter complete information",
  "modalContentNoUsername": "Please enter your username",
  "modalContentNoPassword": "Please enter your password",
  "modalContentLoginFailed": "Login failed, please check if the password is correct",
  "modalContentVerifyCodeError": "Verification code error",
  "modalContentNotRegister": "The account is not registered, please register an account before logging in",
  "modalContentAlreadyRegister": "The account has been registered",
  "modalContentConfirmLogout": "Are you sure you want to log out ?",
  "modalContentTokenInvalid": "Login expired, please log in again",
  "modalContentRegisterSucceeded": "registration success",
  "modalContentRegisterFailed": "registration failed",
  "modalContentNoNetwork": "Please connect to the network",
  "modalContentRequestFailed": "Request failed, please check if the network is available",
  "modalContentConfirmAdd": "Are you sure you want to add ?",
  "modalContentConfirmEdit": "Are you sure you want to edit ?",
  "modalContentConfirmDelete": "Are you sure you want to delete ?",
  "modalContentAddSucceeded": "Added successfully",
  "modalContentAddFailed": "Add failed",
  "modalContentEditSucceeded": "Edited successfully",
  "modalContentEditFailed": "Edit failed",
  "modalContentDeleteSucceeded": "Deleted successfully",
  "modalContentDeleteFailed": "Delete failed",
  "modalContentConfirmAddMember": "Are you sure you want to add new member ?",
  "modalContentConfirmDeleteMember": "Are you sure you want to delete member ?",
  "modalContentConfirmDeleteDevice": "Are you sure you want to delete the device \"{v0}\" ?",
  "modalContentAddDeviceSucceeded": "Device added successfully",
  "modalContentAddDeviceFailed": "Failed to add device",
  "modalContentDeleteDeviceSucceeded": "Device deleted successfully",
  "modalContentDeleteDeviceFailed": "Failed to delete device",
  "modalContentUserNotRegister": "The user is not registered",
  "modalContentUserAlreadyExist": "The user has been added as a member",
  "modalContentNoDeviceName": "Please enter the device name",
  "modalContentNoItemSelected": "Please select at least one option",
  "modalContentConfirmAuth": "Are you sure you want to authorize ?",
  "modalContentConfirmCancelAuth": "Are you sure you want to cancel authorization ?",
  "modalContentAuthSucceeded": "Authorization successful",
  "modalContentAuthFailed": "Authorization failed",
  "modalContentCancelAuthSucceeded": "Authorization canceled successfully",
  "modalContentCancelAuthFailed": "Authorization cancellation failed",
  "modalContentSetLanguage": "Are you sure you want to change the language (the application will restart) ?",
  "modalBtnConfirm": "Confirm",
  "modalBtnCancel": "Cancel",
  
  "paginationPrevText": "Previous page",
  "paginationNextText": "Next Page",
  
  "defaultTitle": "Title",
  "defaultEmpty": "No data",
  
  "keywordTo": "to",
  "keywordXiaoMi": "XiaoMi",
  "keywordHuawei": "Huawei",
  "keywordUnlock": "Unlock",
  "keywordLock": "Lock",
  "keywordOperationType": "Operation Type",
  "keywordOperationDate": "Operation Date",
  
  "QaH": QaH_en,
  "protocol_": protocol_en,
  "privacy_": privacy_en,
};