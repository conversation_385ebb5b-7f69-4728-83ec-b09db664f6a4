{"id": "lime-liquid", "displayName": "lime-liquid 水波进度球-水球图", "version": "0.1.1", "description": "lime-liquid vue为纯css实现水波进度球，uvue app 为drawable实现,优雅展示百分比数据的水球图,兼容uniapp/uniappx(h5,ios,安卓)", "keywords": ["liquid", "水波", "水波球", "进度条", "水球图"], "repository": "", "engines": {"HBuilderX": "^4.26"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": ["lime-style", "lime-shared"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "n"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-uvue": "y", "app-nvue": "n", "app-harmony": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "u", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "钉钉": "y", "快手": "y", "飞书": "y", "京东": "y"}, "快应用": {"华为": "u", "联盟": "u"}}}}}